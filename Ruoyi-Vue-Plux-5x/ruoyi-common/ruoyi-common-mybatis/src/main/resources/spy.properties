# p6spy æ§è½åææä»¶éç½®æä»¶
modulelist=com.baomidou.mybatisplus.extension.p6spy.MybatisPlusLogFactory,com.p6spy.engine.outage.P6OutageFactory
# èªå®ä¹æ¥å¿æå°
logMessageFormat=com.baomidou.mybatisplus.extension.p6spy.P6SpyLogger
#æ¥å¿è¾åºå°æ§å¶å°
appender=com.baomidou.mybatisplus.extension.p6spy.StdoutLogger
# ä½¿ç¨æ¥å¿ç³»ç»è®°å½ sql
#appender=com.p6spy.engine.spy.appender.Slf4JLogger
# åæ¶JDBC URLåç¼
useprefix=true
# éç½®è®°å½ Log ä¾å¤,å¯å»æçç»æéæerror,info,batch,debug,statement,commit,rollback,result,resultset.
excludecategories=info,debug,result,commit,resultset
# æ¥ææ ¼å¼
dateformat=yyyy-MM-dd HH:mm:ss
# SQLè¯­å¥æå°æ¶é´æ ¼å¼
databaseDialectTimestampFormat=yyyy-MM-dd HH:mm:ss
# æ¯å¦è¿æ»¤ Log
filter=true
# è¿æ»¤ Log æ¶ææé¤ç sql å³é®å­ï¼ä»¥éå·åé
exclude=
