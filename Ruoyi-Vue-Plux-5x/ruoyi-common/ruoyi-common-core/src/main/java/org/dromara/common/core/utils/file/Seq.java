package org.dromara.common.core.utils.file;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 序列号生成工具类
 */
public class Seq {

    // 序列号类型枚举
    public enum SeqType {
        DEFAULT, // 默认类型
        UPLOAD   // 用于文件上传的类型
    }

    // 用于生成序列号的原子计数器
    private static final AtomicLong DEFAULT_COUNTER = new AtomicLong(0);
    private static final AtomicLong UPLOAD_COUNTER = new AtomicLong(0);
    // 用于文件上传的序列号类型
    public static final SeqType uploadSeqType = SeqType.UPLOAD;
    /**
     * 根据序列号类型生成唯一ID
     *
     * @param seqType 序列号类型
     * @return 生成的唯一ID
     */
    public static long getId(SeqType seqType) {
        switch (seqType) {
            case UPLOAD:
                return UPLOAD_COUNTER.incrementAndGet();
            case DEFAULT:
            default:
                return DEFAULT_COUNTER.incrementAndGet();
        }
    }

    /**
     * 重置序列号计数器
     *
     * @param seqType 序列号类型
     */
    public static void reset(SeqType seqType) {
        switch (seqType) {
            case UPLOAD:
                UPLOAD_COUNTER.set(0);
                break;
            case DEFAULT:
            default:
                DEFAULT_COUNTER.set(0);
                break;
        }
    }
}
