package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectAccountPermission;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目账户权限视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectAccountPermission.class)
public class ProjectAccountPermissionVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 账户类型ID
     */
    private Integer accountTypeId;

    /**
     * 类型ID
     */
    private Integer typeId;

    /**
     * 权限ID
     */
    private Integer permissionId;

    /**
     * 选项ID
     */
    private Integer optionId;

    /**
     * 账户类型名称
     */
    private String accountTypeName;
}
