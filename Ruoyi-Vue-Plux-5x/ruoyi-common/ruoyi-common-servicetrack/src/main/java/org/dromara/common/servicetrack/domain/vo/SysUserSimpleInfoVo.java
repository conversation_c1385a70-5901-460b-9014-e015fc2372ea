package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectMember;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = ProjectMember.class)
public class SysUserSimpleInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long userId;

    /**
     * user name
     */
    private String userName;

    /**
     * nick name
     */
    private String nickName;

    /*
     * email
     */
    private String email;

    /**
     * external user id
     */
    private Integer externalUserId;

    /**
     * user type
     */
    private Integer stUserType;
}
