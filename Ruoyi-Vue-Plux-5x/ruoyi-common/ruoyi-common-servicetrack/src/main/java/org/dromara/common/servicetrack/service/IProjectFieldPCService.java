package org.dromara.common.servicetrack.service;

import org.dromara.common.servicetrack.domain.bo.ProjectFieldPCBinderBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcSelectionModelVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcSelectionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcVo;

import java.util.List;

public interface IProjectFieldPCService {
    /**
     * 查询项目字段父子关系列表
     *
     * @param projectId 项目ID
     * @return 项目字段父子关系列表
     */
    List<ProjectFieldPcVo> selectFieldPcList(Integer projectId);
   /**
     * 查询项目字段父子选择项关系列表
     *
     * @param projectId 项目ID
     * @return 项目字段父子选择项关系列表
     */
    List<ProjectFieldPcSelectionVo> selectFieldPcSelectionList(Integer projectId);

    /**
     * 查询项目字段父子选择项关系列表
     *
     * @param projectId 项目ID
     * @param parentFieldId 父字段ID
     * @param childFieldId 子字段ID
     * @return 项目字段父子选择项关系列表
     */
    ProjectFieldPcSelectionModelVo getOneFieldPCSelection(Integer projectId, Integer parentFieldId, Integer childFieldId);

    /**
     * 更新项目字段父子关系
     *
     * @param bo 项目字段父子关系
     * @return 结果
     */
    Boolean updateByBo(ProjectFieldPCBinderBo bo);

    /**
     * 删除项目字段父子关系
     *
     * @param projectId 项目ID
     * @param parentFieldId 父字段ID
     * @param childFieldId 子字段ID
     * @return 结果
     */
    Boolean deleteWithValid(Integer projectId,Integer parentFieldId,Integer childFieldId, Boolean isValid);
}
