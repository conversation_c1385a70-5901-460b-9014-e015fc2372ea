package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.NotificationEmailTemplate;

/**
 * 通知邮件模板业务对象 notification_email_template
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NotificationEmailTemplate.class)
public class NotificationEmailTemplateBo extends STBaseEntity {
    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Integer templateId;

    /**
     * 模板名称
     */
    @NotNull(message = "模板名称不能为空")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String templateName;
    /**
     * 邮件主题
     */
    @NotNull(message = "邮件主题不能为空")
    @Size(max = 200, message = "邮件主题长度不能超过200个字符")
    private String emailSubject;

    /**
     * 邮件内容
     */
    private String emailBody;
}
