package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 工作流状态字段数据 workflow_state_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("workflow_state_field")
public class WorkflowStateField extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * state_id
     */
    @TableField(value = "state_id")
    private Integer stateId;

    /**
     * field_id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * option_id
     */
    @TableField(value = "option_id")
    private Integer optionId;
}
