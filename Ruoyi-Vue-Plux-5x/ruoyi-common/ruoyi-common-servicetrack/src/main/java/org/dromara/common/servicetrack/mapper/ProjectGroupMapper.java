package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectGroup;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupVo;

import java.util.List;

/**
 * 项目分组 数据层
 *
 * <AUTHOR>
 */
public interface ProjectGroupMapper extends BaseMapperPlus<ProjectGroup, ProjectGroupVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectGroup> buildWrapper(ProjectGroupBo bo) {
        LambdaQueryWrapper<ProjectGroup> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectGroup::getProjectId, bo.getProjectId());
        lqw.eq(bo.getGroupId() != null, ProjectGroup::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), ProjectGroup::getGroupName, bo.getGroupName());
        return lqw;
    }

    /**
     * 查询项目分组列表
     *
     * @param projectId 项目ID
     * @return 项目分组列表
     */
    List<ProjectGroupVo> selectGroupList(@Param("projectId") Integer projectId);
}
