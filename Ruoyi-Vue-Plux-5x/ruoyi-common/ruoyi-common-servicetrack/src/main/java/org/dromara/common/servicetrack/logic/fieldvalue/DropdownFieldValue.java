package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.constant.eCultureCode;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.constant.eUserSystemFieldDef;
import org.dromara.common.servicetrack.constant.eContactSystemFieldDef;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.utils.ValueConvert;

import java.util.ArrayList;
import java.util.Objects;

public class DropdownFieldValue extends BaseFieldValue {
    private Boolean invalidValue;
    private final Integer projectId;
    private final Integer fieldId;
    private Integer choiceId;
    private final eSTModuleIDDef moduleIDDef;
    public DropdownFieldValue(Integer projectId, eSTModuleIDDef moduleIDDef,Integer fieldId) {
        this.projectId = projectId;
        this.fieldId = fieldId;
        this.moduleIDDef = moduleIDDef;
    }

    @Override
    public Object getRawValue() {
        return choiceId;
    }

    @Override
    public String getDisplayValue() {
        if (choiceId == null || choiceId == 0) {
            return "";
        }
        ProjectManager helper = ProjectManager.getInstance(projectId);
        if( this.moduleIDDef == eSTModuleIDDef.Incident) {
            if (fieldId == eSystemFieldDef.Status.getValue()) {
                return helper.getWorkflowStateName(choiceId);
            } else if (fieldId == eSystemFieldDef.Owner.getValue()) {
                return helper.getProjectMemberNickName(choiceId);
            }
            else if( fieldId == eSystemFieldDef.Employee.getValue()){
                return helper.getSysUserNickName(choiceId);
            }
            else if( fieldId == eSystemFieldDef.Customer.getValue()){
                return helper.getCustomerName(choiceId);
            }
        }
        else if (this.moduleIDDef == eSTModuleIDDef.UserInfo || this.moduleIDDef == eSTModuleIDDef.ContactInfo){
            if (fieldId == eUserSystemFieldDef.CreatedBy.getValue() || fieldId == eContactSystemFieldDef.CreatedBy.getValue()) {
                return helper.getSysUserNickName(choiceId);
            }
            else if( fieldId == eUserSystemFieldDef.Depart.getValue() || fieldId == eContactSystemFieldDef.Depart.getValue()){
                return helper.getDeptNameById(ValueConvert.readLong(choiceId));
            }
        }
        return helper.getChoiceName(fieldId, choiceId);
    }

    @Override
    public void readValueFromDB(Object data) {
        choiceId = 0;
//        if (FieldIdHelper.isCustomField(fieldId)) {
//            if (!ValueConvert.isNull(data) && data instanceof String) {
//                String choiceName = (String) data;
//                if (!choiceName.isEmpty()) {
//                    ProjectHelper helper = ProjectHelper.getInstance(projectId);
//                    ChoiceObject choice = helper.getFieldChoiceByName(fieldId, choiceName, 0);
//                    if (choice != null) {
//                        choiceId = choice.getId();
//                    }
//                }
//            }
//        }
//        else
        {
            if (!ValueConvert.isNull(data)) {
                if( data instanceof ArrayList<?> listData) {
                    if (!listData.isEmpty()) {
                        choiceId = (Integer) listData.get(0);
                    }
                }
                else
                    choiceId = ValueConvert.readInt(data);
            }
        }
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        invalidValue = false;
        if (value == null) {
            choiceId = 0;
        } else {
            String str = value.toString();
            if (str.isEmpty()) {
                choiceId = 0;
            } else if (option == 1) {
                //to do
//                ProjectHelper helper = ProjectHelper.getInstance(projectId);
//                ChoiceObject choice = helper.getChoiceByName(fieldId, str);
//                if (choice != null) {
//                    choiceId = choice.getId();
//                }
//                else
                {
                    choiceId = 0;
                }
            } else {
                try {
                    choiceId = ValueConvert.readInt(str);
                } catch (NumberFormatException e) {
                    invalidValue = true;
                }
            }
        }
    }

    @Override
    public Boolean equalsTo(IFieldValue other) {
        return equals2((DropdownFieldValue) other);
    }

    private Boolean equals2(DropdownFieldValue other) {
        if (other == null) {
            return false;
        }
        return Objects.equals(choiceId, other.choiceId);
    }

    @Override
    public String toCustomFieldFormatString() {

        if (choiceId != 0) {
//            ProjectManager helper = ProjectManager.getInstance(projectId);
//            return helper.getFieldChoiceName(fieldId, choiceId, eCultureCode.DEFAULT);
            return  getDisplayValue();
        }
        else
        {
            return "";
        }
    }

    public Boolean isInvalid() {
        return invalidValue;
    }

    @Override
    public Boolean isUnassigned() {
        return choiceId == null || choiceId == 0 || choiceId == -1;
    }
}
