package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;

/**
 *  Item_changelog
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_changelog")
public class STItemChangelog extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;
    /**
     * changelog Id
     */
    @TableField(value = "changelog_id")
    private Integer changelogId;

    /**
     * log_time
     */
    @TableField(value = "log_time")
    private Date logTime;

    /**
     * createdBy_id
     */
    @TableField(value = "changedby_id")
    private Integer changedById;
}
