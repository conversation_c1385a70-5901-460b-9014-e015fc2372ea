package org.dromara.common.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectGroup;

/**
 * 项目分组业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectGroup.class)
public class ProjectGroupBo extends STBaseEntity {
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 分组ID
     */
    @NotNull(message = "分组ID不能为空")
    private Integer groupId;

    /**
     * 分组名称
     */
    @NotNull(message = "分组名称不能为空")
    @Xss(message = "分组名称不能包含脚本字符")
    @NotBlank(message = "分组名称不能为空")
    @Size(min = 1, max = 200, message = "分组名称的长度不能超过{max}")
    private String groupName;

    /**
     * 分组负责人
     */
    private Integer groupOwner;
}
