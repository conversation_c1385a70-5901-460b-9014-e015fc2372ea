package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 对象 notification_recipient_emailaddress
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("notification_recipient_emailaddress")
public class NotificationRecipientEmailAddress extends STBaseEntity {
    /**
     * key_id
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project_id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * rule_id
     */
    @TableField(value = "rule_id")
    private Integer ruleId;

    /**
     * email_address
     */
    @TableField(value = "email_address")
    private String emailAddress;
}
