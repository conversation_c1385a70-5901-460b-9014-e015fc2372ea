package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 工作流转换字段数据 workflow_transition_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("workflow_transition_field")
public class WorkflowTransitionField extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * transition_id
     */
    @TableField(value = "transition_id")
    private Integer transitionId;

    /**
     * field_id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * option_id
     */
    @TableField(value = "option_id")
    private Integer optionId;
}
