package org.dromara.common.servicetrack.service.impl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.WorkflowTransitionMapper;
import org.dromara.common.servicetrack.service.IWorkflowTransitionService;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 工作流转变管理 服务层实现
 *
 * <AUTHOR> <PERSON>
 */
@RequiredArgsConstructor
@Service
public class WorkflowTransitionServiceImpl implements IWorkflowTransitionService {
    private final WorkflowTransitionMapper workflowTransitionMapper;

    @Override
    public List<WorkflowTransitionVo> selectTransitionList(WorkflowTransitionBo bo, boolean fromCache) {
        if(fromCache)
        {
            return ProjectManager.getInstance(bo.getProjectId()).getWorkflowTransitions();
        }

        return workflowTransitionMapper.selectVoList(workflowTransitionMapper.buildWrapper(bo));
    }
}
