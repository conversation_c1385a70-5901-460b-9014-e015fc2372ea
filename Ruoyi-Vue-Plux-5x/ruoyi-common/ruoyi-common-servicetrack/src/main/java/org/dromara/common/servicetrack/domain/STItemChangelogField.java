package org.dromara.common.servicetrack.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 *  Item_changelog field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_changelog_field")
public class STItemChangelogField extends STBaseEntity {

    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;

    /**
     * field id
     */
    @TableField(value = "field_id")
    private Integer fieldId;
    /**
     * changelog Id
     */
    @TableField(value = "changelog_id")
    private Integer changelogId;

    /**
     * change_from
     */
    @TableField(value = "change_from")
    private String changeFrom;

    /**
     * change_to
     */
    @TableField(value = "change_to")
    private String changeTo;

    /**
     * description
     */
    @TableField(value = "description")
    private String description;
}
