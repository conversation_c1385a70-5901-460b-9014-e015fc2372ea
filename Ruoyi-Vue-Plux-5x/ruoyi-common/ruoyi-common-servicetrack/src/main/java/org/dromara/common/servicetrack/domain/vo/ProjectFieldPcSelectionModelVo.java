package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectFieldPcSelection;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 项目字段父子选择项关系视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectFieldPcSelection.class)
public class ProjectFieldPcSelectionModelVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 父字段ID
     */
    private Integer parentFieldId;

    /**
     * 子字段ID
     */
    private Integer childFieldId;

    private List<ProjectFieldPCSelectionInfoBo> selections;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectFieldPCSelectionInfoBo extends STBaseEntity {
        /**
         * 父选择项ID
         */
        private Integer parentChoiceId;
        private String parentChoiceName;

        /**
         * 子选择项
         */
        private List<SelectionChoiceInfoBo> childChoices;
    }
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class SelectionChoiceInfoBo extends STBaseEntity {
        /**
         * choice id
         */
        private Integer choiceId;
        /**
         * choice name
         */
        private String choiceName;
    }
}
