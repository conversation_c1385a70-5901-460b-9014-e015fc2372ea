package org.dromara.common.servicetrack.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 工作流转换下一个状态VO
 *
 * <AUTHOR> fei
 */

@Data
@NoArgsConstructor
public class WorkflowTransitionNextStateVo implements Serializable {
    /**
     * Transition_id
     */
    private Integer transitionId;

    /**
     * Transition_name
     */
    private String transitionName;

    /**
     * To_state_id
     */
    private Integer toStateId;

    /**
     * To_state_name
     */
    private String toStateName;

    /**
     * Display_order
     */
    private Integer displayOrder;

    /**
     * State_option_id
     */
    private Integer StateOptionId;

    public WorkflowTransitionNextStateVo(Integer transitionId, String transitionName, Integer toStateId, String toStateName, Integer displayOrder, Integer StateOptionId) {
        this.transitionId = transitionId;
        this.transitionName = transitionName;
        this.toStateId = toStateId;
        this.toStateName = toStateName;
        this.displayOrder = displayOrder;
        this.StateOptionId = StateOptionId;
    }
}
