package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectItemType;
import org.dromara.common.servicetrack.domain.ProjectPageAction;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.bo.ProjectItemTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectItemTypeVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageVo;
import org.dromara.common.servicetrack.mapper.ProjectItemTypeMapper;
import org.dromara.common.servicetrack.mapper.ProjectPageActionMapper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectItemTypeService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ProjectItemTypeServiceImpl implements IProjectItemTypeService {
    private final ProjectItemTypeMapper baseMapper;
    private final TableSequenceManager sequenceManager;
    private final ProjectPageActionMapper projectPageActionMapper;

    @Override
    public ProjectItemTypeVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    @Override
    public TableDataInfo<ProjectItemTypeVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectItemTypeBo bo = new ProjectItemTypeBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectItemTypeVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectItemTypeVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    private Wrapper<ProjectItemType> buildQueryWrapper(ProjectItemTypeBo bo) {
        QueryWrapper<ProjectItemType> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getTypeId() != null, "type_id", bo.getTypeId());
        wrapper.like(StringUtils.isNotBlank(bo.getTypeName()), "type_name", bo.getTypeName());
        wrapper.eq(bo.getTransitionId() != null, "transition_id", bo.getTransitionId());
        return wrapper;
    }
    public List<ProjectItemTypeVo> selectItemTypeList(Integer projectId){
        ProjectItemTypeBo bo = new ProjectItemTypeBo();
        bo.setProjectId(projectId);
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }
    @Override
    public Integer insertByBo(ProjectItemTypeBo bo) {
        int typeId = sequenceManager.getNextSequence(SequenceTable.Project_Item_Type, bo.getProjectId());
        bo.setTypeId(typeId);
        ProjectItemType add = MapstructUtils.convert(bo, ProjectItemType.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? bo.getTypeId() : 0;
    }

    @Override
    public Integer updateByBo(ProjectItemTypeBo bo) {
        ProjectItemType update = MapstructUtils.convert(bo, ProjectItemType.class);
        return baseMapper.updateById(update) > 0 ? 1 : 0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        //check if these item type are used in project_page_action table
        // First get the item type ids from Project item type table
        var itemTypeList = baseMapper.selectVoByIds(keyIds);
        var typeIds = itemTypeList.stream().map(ProjectItemTypeVo::getTypeId).toList();
        if (typeIds.isEmpty()) {
            return true;
        }
        QueryWrapper<ProjectPageAction> wrapper = new QueryWrapper<>();
        wrapper.in("itemtype_id", typeIds);
        if (projectPageActionMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("有些条目类型正在使用中,不能删除！");
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }
}
