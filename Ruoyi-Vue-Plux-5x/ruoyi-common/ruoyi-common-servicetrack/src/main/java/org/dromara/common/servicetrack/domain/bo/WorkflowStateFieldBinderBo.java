package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 工作流状态字段绑定对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowStateFieldBinderBo extends STBaseEntity {

    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;
    @NotNull(message = "状态ID不能为空")
    @Min(value = 1, message = "stateId 必须大于 0")
    private Integer stateId;

    private List<WorkflowStateFieldInfoBo> fields;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class WorkflowStateFieldInfoBo extends STBaseEntity{

        private Long id;
        /**
         * 字段ID
         */
        @NotNull(message = "字段ID不能为空")
        private Integer fieldId;

        /**
         * 选项ID
         */
        private Integer optionId;
    }
}
