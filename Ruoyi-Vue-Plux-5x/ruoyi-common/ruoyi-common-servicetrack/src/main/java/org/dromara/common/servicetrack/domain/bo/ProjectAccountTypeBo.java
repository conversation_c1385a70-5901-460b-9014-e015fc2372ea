package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import org.dromara.common.servicetrack.domain.ProjectAccountType;

/**
 * 项目账户类型业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectAccountType.class)
public class ProjectAccountTypeBo extends STBaseEntity {
    private Long id;
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 账户类型ID
     */
    @NotNull(message = "账户类型ID不能为空")
    private Integer accountTypeId;

    /**
     * 类型Id
     */
    private Integer typeId;

    /**
     * 账户类型名称
     */
    @NotNull(message = "账户类型名称不能为空")
    @Xss(message = "账户类型名称不能包含脚本字符")
    @NotBlank(message = "账户类型名称不能为空")
    @Size(min = 0, max = 100, message = "账户类型名称的长度不能超过{max}")
    private String accountTypeName;
}
