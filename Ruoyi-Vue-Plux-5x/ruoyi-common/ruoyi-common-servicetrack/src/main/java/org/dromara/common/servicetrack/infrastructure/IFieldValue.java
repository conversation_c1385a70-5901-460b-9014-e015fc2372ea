package org.dromara.common.servicetrack.infrastructure;

public interface IFieldValue {
    String getDisplayValue();
    Object getValueObject();
    Object getAdditionalInfo();
    Object getRawValue();
    void readValueFromDB(Object data);
    void setFieldValue(Object value, Integer option);
    Boolean equalsTo(IFieldValue other);
    String toCustomFieldFormatString();
    Boolean isUnassigned();
}
