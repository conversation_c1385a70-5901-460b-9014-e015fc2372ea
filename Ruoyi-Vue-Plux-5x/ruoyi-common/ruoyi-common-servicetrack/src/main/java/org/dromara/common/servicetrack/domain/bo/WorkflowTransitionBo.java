package org.dromara.common.servicetrack.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.WorkflowTransition;

import java.util.Date;

/**
 * 工作流转换Bo
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorkflowTransition.class)
public class WorkflowTransitionBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * transition_id
     */
    private Integer transitionId;

    /**
     * transition_name
     */
    private String transitionName;

    /**
     * from_state_id
     * /
     */
    private Integer fromStateId;
    /**
     * From_state_identifier
     */
    private String fromStateIdentifier;

    /**
     * to_state_id
     */
    private Integer toStateId;
    /**
     * To_state_identifier
     */
    private String toStateIdentifier;

    /**
     * display_order
     */
    private Integer displayOrder;

    /**
     * transition_identifier
     */
    private String transitionIdentifier;

    /**
     * transition_description
     */
    private String transitionDescription;
}
