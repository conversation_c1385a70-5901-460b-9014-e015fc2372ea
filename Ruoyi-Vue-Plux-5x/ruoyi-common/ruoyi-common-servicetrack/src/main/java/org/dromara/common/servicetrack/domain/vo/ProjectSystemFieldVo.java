package org.dromara.common.servicetrack.domain.vo;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectSystemField;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 条目视图对象 item_info
 *
 * <AUTHOR> fei
 */
@Data
@AutoMapper(target = ProjectSystemField.class)
public class ProjectSystemFieldVo implements Serializable{
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    private Integer projectId;

    /**
     * field id
     */
    private Integer fieldId;

    /**
     * field name
     */
    private String fieldName;

    /**
     * field type
     */
    private Integer fieldType;

    /**
     * module id
     */
    private Integer moduleId;

    /**
     * field default name
     */
    private String fieldDefaultName;
}
