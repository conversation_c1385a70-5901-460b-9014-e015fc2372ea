package org.dromara.common.servicetrack.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.NotificationEmailLog;
import org.dromara.common.servicetrack.domain.bo.NotificationEmailLogBo;
import org.dromara.common.servicetrack.domain.vo.NotificationEmailLogVo;

/**
 * notification_email_log表的mapper接口
 * <AUTHOR> fei
 */
public interface NotificationEmailLogMapper  extends BaseMapperPlus<NotificationEmailLog, NotificationEmailLogVo> {
    /**
     * 查询notification_email_log表的所有数据
     *
     * @return 返回notification_email_log表的所有数据
     */
    List<NotificationEmailLogVo> selectAll();

    /**
     * 根据主键查询notification_email_log表的数据
     *
     * @param logId 主键值
     * @return 返回主键对应的数据
     */
    NotificationEmailLogVo selectByPk(@Param("logId") Integer logId);


    /**
     * 删除notification_email_log表中的数据
     *
     * @return 返回删除数据影响的行数
     */
    int deleteByQueueIds(@Param("listLogIds") List<Integer> logIds);
}
