package org.dromara.common.servicetrack.sequence;

import java.util.List;
import java.util.Map;

/**
 * 序列号数据库访问接口
 *
 * <AUTHOR> Li
 */
public interface SequenceRepository {

    /**
     * 批量获取多个表的最大序列号
     *
     * @param tableInfos 表信息列表，每个元素是一个Map，包含tableName和sequenceColumn
     * @return 表名和最大序列号的映射列表
     */
    List<Map<String, Object>> getMaxSequences(List<Map<String, String>> tableInfos);

    /**
     * 获取指定父ID下的最大序列号
     *
     * @param tableName 表名
     * @param sequenceColumn 序列号列名
     * @param parentIdColumn 父ID列名
     * @param parentId 父ID值
     * @return 最大序列号
     */
    int getMaxSequenceByParent(String tableName, String sequenceColumn, String parentIdColumn, int parentId);

    /**
     * 批量获取多个表中所有父ID及其对应的最大序列号
     *
     * @param tableInfos 表信息列表，每个元素是一个Map，包含tableName、sequenceColumn和parentIdColumn
     * @return 表名、父ID和最大序列号的映射列表
     */
    List<Map<String, Object>> getAllParentMaxSequences(List<Map<String, String>> tableInfos);
}
