package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 工作流信息数据 workflow_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("workflow_info")
public class WorkflowInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * workflow_id
     */
    @TableField(value = "workflow_id")
    private Integer workflowId;

    /**
     * workflow_name
     */
    @TableField(value = "workflow_name")
    private String workflowName;

    /**
     * workflow_content
     */
    @TableField(value = "workflow_content")
    private String workflowContent;
}
