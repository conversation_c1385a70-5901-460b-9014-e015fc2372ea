package org.dromara.common.servicetrack.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * project_field_calculation表的实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_calculation")
public class ProjectFieldCalculation extends STBaseEntity {


    /**
     * key_id
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project_id
     */
    @TableField(value ="project_id")
    private Integer projectId;

    /**
     * field_id
     */
    @TableField(value ="field_id")
    private Integer fieldId;

    /**
     * formula
     */
    @TableField(value ="formula")
    private String formula;
}
