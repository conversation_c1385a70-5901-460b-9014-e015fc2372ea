package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectPageAction;
import org.dromara.common.servicetrack.domain.bo.ProjectPageActionBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo;

import java.util.List;

public interface ProjectPageActionMapper extends BaseMapperPlus<ProjectPageAction, ProjectPageActionVo> {
    default LambdaQueryWrapper<ProjectPageAction> buildWrapper(ProjectPageActionBo bo) {
        LambdaQueryWrapper<ProjectPageAction> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectPageAction::getProjectId, bo.getProjectId());
        lqw.eq(bo.getItemTypeId() != null, ProjectPageAction::getItemTypeId, bo.getItemTypeId());
        lqw.eq(bo.getPageId() != null, ProjectPageAction::getPageId, bo.getPageId());
        lqw.eq(bo.getActionId() != null, ProjectPageAction::getActionId, bo.getActionId());
        lqw.eq(bo.getPageOrder() != null, ProjectPageAction::getPageOrder, bo.getPageOrder());
        return lqw;
    }

    List<ProjectPageActionVo> selectPageActionList(@Param("projectId") Integer projectId);
}
