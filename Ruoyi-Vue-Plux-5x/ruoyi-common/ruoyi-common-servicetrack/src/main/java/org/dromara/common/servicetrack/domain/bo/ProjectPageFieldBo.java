package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectPageField;

import java.util.Date;

/**
 * 条目业务对象 item_info
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectPageField.class)
public class ProjectPageFieldBo  extends STBaseEntity{
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 页面ID
     */
    @NotNull(message = "页面ID不能为空")
    private Integer pageId;
    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    /**
     * 页面行数
     */
    @NotNull(message = "页面行数不能为空")
    private Integer pageRow;

    /**
     * 页面列数
     */
    @NotNull(message = "页面列数不能为空")
    @Min(value = 1, message = "页面列数不能小于1")
    private Integer pageColumn;
}
