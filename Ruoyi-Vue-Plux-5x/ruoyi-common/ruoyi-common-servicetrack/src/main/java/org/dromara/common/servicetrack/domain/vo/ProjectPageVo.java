package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectPage;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = ProjectPage.class)
public class ProjectPageVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer projectId;
    private Integer pageId;
    private String pageName;
    private Integer moduleId;
}
