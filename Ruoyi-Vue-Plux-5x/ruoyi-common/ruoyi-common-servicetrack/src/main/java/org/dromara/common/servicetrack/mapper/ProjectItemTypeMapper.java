package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectItemType;
import org.dromara.common.servicetrack.domain.bo.ProjectItemTypeBo;
import org.dromara.common.servicetrack.domain.vo.ProjectItemTypeVo;

public interface ProjectItemTypeMapper extends BaseMapperPlus<ProjectItemType, ProjectItemTypeVo> {
    default LambdaQueryWrapper<ProjectItemType> buildWrapper(ProjectItemTypeBo bo) {
        LambdaQueryWrapper<ProjectItemType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectItemType::getProjectId, bo.getProjectId());
        lqw.eq(bo.getTypeId() != null, ProjectItemType::getTypeId, bo.getTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getTypeName()), ProjectItemType::getTypeName, bo.getTypeName());
        lqw.eq(bo.getTransitionId() != null, ProjectItemType::getTransitionId, bo.getTransitionId());
        return lqw;
    }
}
