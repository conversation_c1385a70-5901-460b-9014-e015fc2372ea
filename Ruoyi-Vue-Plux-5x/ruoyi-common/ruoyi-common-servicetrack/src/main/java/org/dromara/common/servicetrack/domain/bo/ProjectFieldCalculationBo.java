package org.dromara.common.servicetrack.domain.bo;

import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectAccountType;
import org.dromara.common.servicetrack.domain.ProjectFieldCalculation;

/**
 * project_field_calculation表的bo类
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectFieldCalculation.class)
public class ProjectFieldCalculationBo extends STBaseEntity {

    /**
     * key_id
     */
    private Long id;

    /**
     * project_id
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * field_id
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    /**
     * formula
     */
    private String formula;
}
