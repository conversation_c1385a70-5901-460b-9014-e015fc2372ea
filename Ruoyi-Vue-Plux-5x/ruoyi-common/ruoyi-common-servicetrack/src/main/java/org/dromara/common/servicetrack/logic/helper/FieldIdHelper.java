package org.dromara.common.servicetrack.logic.helper;

import org.dromara.common.servicetrack.constant.eContactSystemFieldDef;
import org.dromara.common.servicetrack.constant.eCustomerSystemFieldDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.constant.eUserSystemFieldDef;
import org.dromara.common.servicetrack.logic.project.ProjectManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class FieldIdHelper {
    public static eSystemFieldDef[] AllModuleSystemFields = Arrays.stream(eSystemFieldDef.values()).filter(e -> e.getValue() > 0).toArray(eSystemFieldDef[]::new);
    public static List<Integer> IndividualSystemFields= List.of();

    public static boolean IsCustomField(Integer fieldId){
        return fieldId > 10000;
    }
    public  static boolean IsAllModuleSystemField(Integer fieldId){
        return fieldId >= 1 && fieldId <= 1000;
    }
    public  static boolean IsIndividualSystemField(Integer fieldId){
        return fieldId > 1000 && fieldId <= 2000;
    }
    public  static boolean IsSystemField(Integer fieldId){
        return IsAllModuleSystemField(fieldId) || IsIndividualSystemField(fieldId);
    }
    public static String getSystemFieldName(Integer projectId, Integer fieldId){
        if(IsAllModuleSystemField(fieldId)){
            boolean isBaseProject = ProjectManager.getInstance(projectId).isBaseProject();
            if( isBaseProject) {
                if (fieldId >= eCustomerSystemFieldDef.CustomerId.getValue() && fieldId <= eCustomerSystemFieldDef.LastField.getValue()) {
                    return eCustomerSystemFieldDef.from(fieldId).getName();
                } else if (fieldId >= eContactSystemFieldDef.UserId.getValue() && fieldId <= eContactSystemFieldDef.LastField.getValue()) {
                    return eContactSystemFieldDef.from(fieldId).getName();
                } else if (fieldId >= eUserSystemFieldDef.UserId.getValue() && fieldId <= eUserSystemFieldDef.LastField.getValue()) {
                    return eUserSystemFieldDef.from(fieldId).getName();
                }
            }
            return eSystemFieldDef.from(fieldId).getName();
        }
        return "";
    }

    public static List<Integer> getDefaultListViewFieldIds(){
       List<Integer> defaultListViewFieldIds = new ArrayList<>();
         defaultListViewFieldIds.addAll(Arrays.asList(
                eSystemFieldDef.IncidentID.getValue(),
                eSystemFieldDef.Title.getValue(),
                eSystemFieldDef.Status.getValue(),
                eSystemFieldDef.Owner.getValue(),
                eSystemFieldDef.SubmittedBy.getValue(),
                eSystemFieldDef.SubmittedTime.getValue()
         ));
       return defaultListViewFieldIds;
    }

    public static eUserSystemFieldDef[] AllUserInfoSystemFields = Arrays.stream(eUserSystemFieldDef.values())
        .filter(x -> x.getValue() < eUserSystemFieldDef.LastField.getValue())
        .toArray(eUserSystemFieldDef[]::new);
    public static eCustomerSystemFieldDef[] AllCustomerInfoSystemFields = Arrays.stream(eCustomerSystemFieldDef.values())
        .filter(x -> x.getValue() < eCustomerSystemFieldDef.LastField.getValue())
        .toArray(eCustomerSystemFieldDef[]::new);
    public static eContactSystemFieldDef[] AllContactSystemFields = Arrays.stream(eContactSystemFieldDef.values())
        .filter(x -> x.getValue() < eContactSystemFieldDef.LastField.getValue())
        .toArray(eContactSystemFieldDef[]::new);

    public static List<Integer> getDefaultUserListViewFieldIds(){
        List<Integer> defaultListViewFieldIds = new ArrayList<>();
        defaultListViewFieldIds.addAll(eUserSystemFieldDef.getSystemFieldIds());
        return defaultListViewFieldIds;
    }
    public  static boolean IsUserInfoSystemField(Integer fieldId){
        return fieldId >= eUserSystemFieldDef.UserId.getValue() && fieldId <= eUserSystemFieldDef.LastField.getValue();
    }
    public  static boolean IsCustomerInfoSystemField(Integer fieldId){
        return fieldId >= eCustomerSystemFieldDef.CustomerId.getValue() && fieldId <= eCustomerSystemFieldDef.LastField.getValue();
    }
    public  static boolean IsContactInfoSystemField(Integer fieldId){
        return fieldId >= eContactSystemFieldDef.UserId.getValue() && fieldId <= eContactSystemFieldDef.LastField.getValue();
    }
    public static List<Integer> getDefaultCustomerListViewFieldIds(){
        List<Integer> defaultListViewFieldIds = new ArrayList<>();
        defaultListViewFieldIds.addAll(eCustomerSystemFieldDef.getSystemFieldIds());
        return defaultListViewFieldIds;
    }
    public static List<Integer> getDefaultContactListViewFieldIds(){
        List<Integer> defaultListViewFieldIds = new ArrayList<>();
        defaultListViewFieldIds.addAll(eContactSystemFieldDef.getSystemFieldIds());
        return defaultListViewFieldIds;
    }
    public static boolean isTimestampField(Integer projectId,Integer fieldId){
        var projectHandler = ProjectManager.getInstance(projectId);
       return projectHandler != null && projectHandler.isTimestampField(fieldId);
    }
}
