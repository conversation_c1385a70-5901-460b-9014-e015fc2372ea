package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.ProjectSystemField;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldCustomBo;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;

import java.util.List;

/**
 * 系统字段管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IProjectSystemFieldService {

    /**
     * 根据条件查询字段列表
     *
     * @param field 字段信息
     * @return 字段集合信息
     */
    List<ProjectFieldVo> selectFieldList(ProjectSystemFieldBo field);
    /**
     * 更新项目字段
     */
    Integer updateByBo(ProjectSystemFieldBo bo);
}
