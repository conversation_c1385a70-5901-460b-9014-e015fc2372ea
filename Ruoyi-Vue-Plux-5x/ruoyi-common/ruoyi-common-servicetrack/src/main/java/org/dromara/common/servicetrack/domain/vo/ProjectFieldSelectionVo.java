package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectFieldSelection;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目视图对象 project_field_selection
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectFieldSelection.class)
public class ProjectFieldSelectionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    private Integer projectId;

    /**
     * field id
     */
    private Integer fieldId;

    /**
     * choice id
     */
    private Integer choiceId;
    /**
     * choice name
     */
    private String choiceName;

    /**
     * choice order
    */
    private Integer choiceOrder;

    /**
     * choice count
     */
    private Integer choiceCount;
}
