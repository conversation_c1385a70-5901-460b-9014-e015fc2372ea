package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.servicetrack.domain.WorkflowInfo;
import org.dromara.common.servicetrack.domain.bo.WorkflowInfoBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowInfoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 工作流信息 数据层
 *
 * <AUTHOR> fei
 */
public interface WorkflowInfoMapper extends BaseMapperPlus<WorkflowInfo, WorkflowInfoVo> {
    default LambdaQueryWrapper<WorkflowInfo> buildWrapper(WorkflowInfoBo bo) {
        LambdaQueryWrapper<WorkflowInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, WorkflowInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, WorkflowInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getWorkflowId() != null, WorkflowInfo::getWorkflowId, bo.getWorkflowId());
        lqw.like(StringUtils.isNotBlank(bo.getWorkflowName()), WorkflowInfo::getWorkflowName, bo.getWorkflowName());

        return lqw;
    }
}
