package org.dromara.common.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.WorkflowTransitionField;

import java.io.Serializable;

/**
 * 工作流转换字段VO
 *
 * <AUTHOR> fei
 */
@Data
@AutoMapper(target = WorkflowTransitionField.class)
public class WorkflowTransitionFieldVo implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * ProjectID
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * transition_id
     */
    @JsonIgnore
    private Integer transitionId;

    /**
     * field_id
     */
    private Integer fieldId;
    /**
     * field_name
     */
    private String fieldName;

    /**
     * option_id
     */
    private Integer optionId;
}
