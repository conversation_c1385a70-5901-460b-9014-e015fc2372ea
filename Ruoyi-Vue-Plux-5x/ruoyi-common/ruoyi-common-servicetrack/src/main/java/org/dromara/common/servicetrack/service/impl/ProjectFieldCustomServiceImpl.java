package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.domain.ProjectFieldCalculation;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldCustomBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldCustomVo;
import org.dromara.common.servicetrack.mapper.ProjectFieldCalculationMapper;
import org.dromara.common.servicetrack.mapper.ProjectFieldCustomMapper;
import org.dromara.common.servicetrack.mapper.ProjectPageFieldMapper;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectFieldCustomService;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.constant.stConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 项目自定义字段 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectFieldCustomServiceImpl implements IProjectFieldCustomService {
    private final ProjectFieldCustomMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;
    private final ProjectPageFieldMapper projectPageFieldMapper;
    private final ProjectFieldCalculationMapper projectfieldCalculationMapper;

    /**
     * 查询项目自定义字段
     */
    @Override
    public ProjectFieldCustomVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    /**
     * 查询项目自定义字段列表
     */
    @Override
    public TableDataInfo<ProjectFieldCustomVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectFieldCustomBo bo = new ProjectFieldCustomBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectFieldCustomVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectFieldCustomVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectFieldCustomVo> queryList(Integer projectId) {
        ProjectFieldCustomBo bo = new ProjectFieldCustomBo();
        bo.setProjectId(projectId);
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    private Wrapper<ProjectFieldCustom> buildQueryWrapper(ProjectFieldCustomBo bo) {
        QueryWrapper<ProjectFieldCustom> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getFieldId() != null, "field_id", bo.getFieldId());
        wrapper.like(StringUtils.isNotBlank(bo.getFieldName()), "field_name", bo.getFieldName());
        wrapper.eq(bo.getModuleId() != null, "module_id", bo.getModuleId());
        wrapper.eq(bo.getFieldType() != null, "field_type", bo.getFieldType());
        return wrapper;
    }

    /**
     * 新增项目自定义字段
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(ProjectFieldCustomBo bo) {
        boolean isAmountFieldWithFormula = false;
        if( bo.getFieldType() == eFieldTypeDef.Amount.getValue() && Objects.equals(bo.getFieldSubtype(), stConstant.Amount_Field_SubType_Formula)){
            isAmountFieldWithFormula = true;
            if( bo.getFormula() == null || StringUtils.isBlank(bo.getFormula()))
                throw new ServiceException("amount field need a formula.");
        }
        int fieldId  = tableSequenceManager.getNextSequence(SequenceTable.Project_Field_Custom,bo.getProjectId());
        bo.setFieldId(fieldId);
        ProjectFieldCustom add = MapstructUtils.convert(bo, ProjectFieldCustom.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            if( isAmountFieldWithFormula){
                ProjectFieldCalculation calc = new ProjectFieldCalculation();
                calc.setProjectId(bo.getProjectId());
                calc.setFieldId(bo.getFieldId());
                calc.setFormula(bo.getFormula());
                projectfieldCalculationMapper.insert(calc);
            }
        }
        return flag ? add.getFieldId() : 0;
    }

    /**
     * 修改项目自定义字段
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByBo(ProjectFieldCustomBo bo) {
        boolean isAmountFieldWithFormula = false;
        if( bo.getFieldType() == eFieldTypeDef.Amount.getValue() && Objects.equals(bo.getFieldSubtype(), stConstant.Amount_Field_SubType_Formula)){
            isAmountFieldWithFormula = true;
            if( bo.getFormula() == null || StringUtils.isBlank(bo.getFormula()))
                throw new ServiceException("amount field need a formula.");
        }
        ProjectFieldCustom update = MapstructUtils.convert(bo, ProjectFieldCustom.class);
        var ret =  baseMapper.updateById(update) > 0 ? bo.getFieldId() : 0;
        if( ret > 0 && isAmountFieldWithFormula) {
            var projectFieldCal = projectfieldCalculationMapper.selectOne(new LambdaQueryWrapper<ProjectFieldCalculation>()
                .eq(ProjectFieldCalculation::getProjectId, bo.getProjectId())
                .eq(ProjectFieldCalculation::getFieldId, bo.getFieldId()));
            boolean isUpdate = true;
            if( projectFieldCal == null){
               isUpdate = false;
            }
            ProjectFieldCalculation calc = new ProjectFieldCalculation();
            if( isUpdate ){
                calc.setId(projectFieldCal.getId());
            }
            calc.setProjectId(bo.getProjectId());
            calc.setFieldId(bo.getFieldId());
            calc.setFormula(bo.getFormula());
            if( !isUpdate ){
                projectfieldCalculationMapper.insert(calc);
            }
            else {
                projectfieldCalculationMapper.updateById(calc);
            }
        }
        return ret;
    }

    /**
     * 批量删除项目自定义字段
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        //check if these fields are used in project_page_field table
        // First get the field_ids from ProjectFieldCustom table
        var customFields = baseMapper.selectVoByIds(keyIds);
        List<Integer> fieldIds = customFields.stream().map(ProjectFieldCustomVo::getFieldId).toList();
        if (fieldIds.isEmpty()) {
            return true;
        }
        QueryWrapper<ProjectPageField> wrapper = new QueryWrapper<>();
        wrapper.eq("project_id", customFields.get(0).getProjectId());
        wrapper.in("field_id", fieldIds);
        if (projectPageFieldMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("有些字段正在使用中,不能删除！");
        }
        var calcFieldIds = customFields.stream().filter(field -> field.getFieldType() == eFieldTypeDef.Amount.getValue()).map(ProjectFieldCustomVo::getFieldId).toList();
        if( !calcFieldIds.isEmpty()) {
            projectfieldCalculationMapper.delete(new QueryWrapper<ProjectFieldCalculation>().eq("project_id", customFields.get(0).getProjectId()).in("field_id", calcFieldIds));
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }
}
