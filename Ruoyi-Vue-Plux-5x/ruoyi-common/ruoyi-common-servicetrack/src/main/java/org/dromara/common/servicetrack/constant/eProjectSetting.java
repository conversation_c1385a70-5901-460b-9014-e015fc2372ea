package org.dromara.common.servicetrack.constant;

public enum eProjectSetting implements IValueEnum {
    NONE(0),
    ListView_SelectedColumns(1),
    Project_Alias(2),
    Project_Email_Setting(3),
    ListView_ColorDefinition(4),
    Last_Setting(1000);


    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eProjectSetting(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static eProjectSetting from(Integer value) {
        return IValueEnum.valueOf(eProjectSetting.class, value);
    }
}
