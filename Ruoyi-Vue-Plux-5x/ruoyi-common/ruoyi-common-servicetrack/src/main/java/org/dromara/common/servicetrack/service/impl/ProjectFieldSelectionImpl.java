package org.dromara.common.servicetrack.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.servicetrack.domain.ProjectFieldSelection;
import org.dromara.common.servicetrack.domain.bo.FieldChoicesBo;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBatchBo;
import org.dromara.common.servicetrack.domain.bo.ProjectSortBinderBo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectFieldSelectionMapper;
import org.dromara.common.servicetrack.service.IProjectFieldSelectionService;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectFieldSelectionImpl implements IProjectFieldSelectionService {
    private final ProjectFieldSelectionMapper baseMapper;

    @Override
    public List<ProjectFieldSelectionVo> selectFieldSelctionList(ProjectFieldSelectionBo field, boolean fromCache) {
        if(fromCache)
        {
            return ProjectManager.getInstance(field.getProjectId()).getProjectFieldSelections();
        }

        var list = baseMapper.selectVoList(baseMapper.buildWrapper(field));

        list.sort(Comparator.comparingInt(ProjectFieldSelectionVo::getFieldId)
            .thenComparingInt(ProjectFieldSelectionVo::getChoiceOrder));

        return list;
    }

    @Override
    public Map<Integer, List<ProjectFieldSelectionVo>> getChoiceIdsByFieldId(FieldChoicesBo field)    {
        return ProjectManager.getInstance(field.getProjectId()).getChoiceIdsByFieldId(field);
    }

    @Override
    public Map<Integer, List<ProjectFieldSelectionVo>> getChildFieldChoiceList(Integer projectId, Integer parentFieldId, Integer parentFieldChoiceId) {
        return ProjectManager.getInstance(projectId).getChildFieldChoiceList(parentFieldId, parentFieldChoiceId);
    }

    private Integer[] GetMaxChoiceIdAndOrder(Integer projectId, Integer fieldId) {
        //get max choice id
        QueryWrapper<ProjectFieldSelection> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COALESCE(MAX(choice_id), 0) as choice_id, COALESCE(MAX(choice_order), 0) as choice_order")
            .eq("project_id", projectId)
            .eq("field_id", fieldId);
        ProjectFieldSelection result = baseMapper.selectOne(queryWrapper);
        int maxChoiceId = 1;
        int maxChoiceOrder = 1;
        if (!ObjectUtil.isNull(result)) {
            maxChoiceId =  result.getChoiceId() + 1;
            maxChoiceOrder = result.getChoiceOrder() + 1;
        }
        return new Integer[]{maxChoiceId, maxChoiceOrder};
    }
    @Override
    public Integer insertByBo(ProjectFieldSelectionBo bo) {
        //get max choice id
        int maxChoiceId = 1;
        int maxChoiceOrder = 1;
        var maxChoiceIdAndOrder = GetMaxChoiceIdAndOrder(bo.getProjectId(), bo.getFieldId());
        maxChoiceId = maxChoiceIdAndOrder[0];
        maxChoiceOrder = maxChoiceIdAndOrder[1];

        bo.setChoiceId(maxChoiceId);
        bo.setChoiceOrder(maxChoiceOrder);
        var add = MapstructUtils.convert(bo, ProjectFieldSelection.class);
        int flag = baseMapper.insert(add);
        if (flag > 0) {
            bo.setId(add.getId());
        }
        //reset cache
        ProjectManager.getInstance(bo.getProjectId()).resetProjectFieldSelections();
        return flag > 0 ? bo.getChoiceId() : 0;
    }

    @Override
    public Integer updateByBo(ProjectFieldSelectionBo bo) {
        var update = MapstructUtils.convert(bo, ProjectFieldSelection.class);

        boolean flag = baseMapper.updateById(update) > 0;
        //reset cache
        if( flag)
            ProjectManager.getInstance(bo.getProjectId()).resetProjectFieldSelections();
        return flag ? bo.getChoiceId() : 0;
    }

    @Override
    public Boolean updateBatchByBo(ProjectFieldSelectionBatchBo bo) {

        var projectId = bo.getProjectId();
        var fieldId = bo.getFieldId();
        if( projectId == null || fieldId == null){
            throw new ServiceException("projectId or fieldId is null");
        }
        if( bo.getSelections() == null || bo.getSelections().isEmpty()) {
            return false;
        }
        var newSelections = bo.getSelections().stream()
            .filter(info -> info.getChoiceId() == null || info.getChoiceId() == 0)
            .toList();
        var updateSelections = bo.getSelections().stream()
            .filter(info -> info.getChoiceId() != null && info.getChoiceId() > 0)
            .toList();

        if( !updateSelections.isEmpty()) {
            List<ProjectFieldSelection> updateSelectionList = new ArrayList<>();
           var existedSelectionVoList =  baseMapper.selectVoList(new QueryWrapper<ProjectFieldSelection>()
                    .eq("project_id", projectId)
                    .eq("field_id", fieldId)
                    .in("choice_id", updateSelections.stream().map(ProjectFieldSelectionBatchBo.ProjectFieldSelectionInfo::getChoiceId).toList()));
           for (var info : updateSelections) {
               var existedSelection = existedSelectionVoList.stream().filter(p -> p.getChoiceId().equals(info.getChoiceId())).findFirst().orElse(null);
               if (existedSelection != null && !existedSelection.getChoiceName().equals(info.getChoiceName())) {
                   existedSelection.setChoiceName(info.getChoiceName());
                   var updateSelection = MapstructUtils.convert(existedSelection, ProjectFieldSelection.class);
                   updateSelectionList.add(updateSelection);
               }
           }
           if (!updateSelectionList.isEmpty()) {
               baseMapper.updateBatchById(updateSelectionList);
           }
        }
        if( !newSelections.isEmpty()) {
            List<ProjectFieldSelection> addSelectionList = new ArrayList<>();
            //get max choice id
            int maxChoiceId = 1;
            int maxChoiceOrder = 1;
            var maxChoiceIdAndOrder = GetMaxChoiceIdAndOrder(bo.getProjectId(), bo.getFieldId());
            maxChoiceId = maxChoiceIdAndOrder[0];
            maxChoiceOrder = maxChoiceIdAndOrder[1];
            for (var info : newSelections) {
                ProjectFieldSelection addSelection = new ProjectFieldSelection();

                addSelection.setProjectId(projectId);
                addSelection.setFieldId(fieldId);
                addSelection.setChoiceId(maxChoiceId++ );
                addSelection.setChoiceName(info.getChoiceName());
                addSelection.setChoiceOrder(maxChoiceOrder ++ );

                addSelectionList.add(addSelection);
            }
            baseMapper.insertBatch(addSelectionList);
        }
        //reset cache
        ProjectManager.getInstance(projectId).resetProjectFieldSelections();
        return true;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {

        //check if these field selections are used in item_selection table
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        var selectList = baseMapper.selectVoByIds(keyIds);
        if (selectList.isEmpty()) {
            return true;
        }
        var projectId = selectList.get(0).getProjectId();
        var fieldIds = selectList.stream().map(ProjectFieldSelectionVo::getFieldId).distinct().toList();
        var choiceIds = selectList.stream().map(ProjectFieldSelectionVo::getChoiceId).distinct().toList();
        if (fieldIds.isEmpty() || choiceIds.isEmpty()) {
            return true;
        }

        //another way: using sql statement directly
//        ProjectFieldSelectionVo selectionInItemSelection = baseMapper.selectVoOne(new QueryWrapper<ProjectFieldSelection>()
//            .select("1 as id, (SELECT COUNT(1) FROM item_selection " +
//                "WHERE project_id = " + projectId + " " +
//                "AND field_id IN (" + String.join(",", fieldIds.stream().map(String::valueOf).toList()) + ") " +
//                "AND choice_id IN (" + String.join(",", choiceIds.stream().map(String::valueOf).toList()) + ")) as choice_id")
//            .eq("project_id", projectId)
//            .inSql("field_id", String.join(",", fieldIds.stream().map(String::valueOf).toList()))
//            .inSql("choice_id", String.join(",", choiceIds.stream().map(String::valueOf).toList())));
//        if (ObjectUtil.isNotNull(selectionInItemSelection) && selectionInItemSelection.getChoiceId() != null && selectionInItemSelection.getChoiceId() > 0) {
//            throw new ServiceException("该字段选择已被项目条目关联使用，不允许删除");
//        }
        //one way: using mybatis-plus mapper xml to do it
        ProjectFieldSelectionVo selectionInItemSelection = baseMapper.selectFieldSelectionByItemSelection(projectId, fieldIds, choiceIds);
        if (ObjectUtil.isNotNull(selectionInItemSelection) && selectionInItemSelection.getChoiceCount() != null && selectionInItemSelection.getChoiceCount() > 0) {
            throw new ServiceException("该字段选择已被项目条目关联使用，不允许删除");
        }
        baseMapper.deleteByIds(keyIds);

        //reset cache
        ProjectManager.getInstance(projectId).resetProjectFieldSelections();
        return true;
    }

    @Override
    public Boolean sortSelection(ProjectSortBinderBo bo) {
        if(bo.getIds()== null || bo.getIds().isEmpty()) {
            return false;
        }
        var selectionList = baseMapper.selectList(new QueryWrapper<ProjectFieldSelection>().lambda().in(ProjectFieldSelection::getId, bo.getIds()));

        var choiceOrder = 1;
        List<ProjectFieldSelection> updateSelectionList = new ArrayList<>();
        for (var Id : bo.getIds()) {
            var selection = selectionList.stream().filter(p -> p.getId().equals(Id)).findFirst().orElse(null);
            if (selection == null) {
                continue;
            }
            selection.setChoiceOrder(choiceOrder++);
            updateSelectionList.add(selection);
        }
        if (!updateSelectionList.isEmpty()) {
            //reset cache
            ProjectManager.getInstance(bo.getProjectId()).resetProjectFieldSelections();
            return  baseMapper.updateBatchById(updateSelectionList);
        }
        return false;
    }
}
