package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 项目页面字段绑定对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPageFieldBinderBo extends STBaseEntity {
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "页面ID不能为空")
    private Integer pageId;

    List<ProjectPageFieldInfoBo> pageFields;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectPageFieldInfoBo extends STBaseEntity{

        private Long id;
        /**
         * 字段ID
         */
        @NotNull(message = "字段ID不能为空")
        private Integer fieldId;

        /**
         * 页面行数
         */
        @NotNull(message = "页面行数不能为空")
        private Integer pageRow;

        /**
         * 页面列数
         */
        @NotNull(message = "页面列数不能为空")
        private Integer pageColumn;
    }

}
