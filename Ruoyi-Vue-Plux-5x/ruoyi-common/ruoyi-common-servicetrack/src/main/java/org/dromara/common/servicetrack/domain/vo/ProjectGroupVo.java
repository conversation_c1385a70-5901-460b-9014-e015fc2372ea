package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectGroup;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目分组视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectGroup.class)
public class ProjectGroupVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 分组ID
     */
    private Integer groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组负责人
     */
    private Integer groupOwner;

    /**
     * 分组负责人名称
     */
    private String groupOwnerName;
}
