package org.dromara.common.servicetrack.constant;

import java.util.Arrays;
import java.util.List;

public interface IValueEnum {

    int getValue();
    static <T extends Enum<T> & IValueEnum> T valueOf(Class<T> enumClass, Integer value) {
        if (value == null) {
            return null;
        }
        for (T enumConstant : enumClass.getEnumConstants()) {
            if (enumConstant.getValue() == value) {
                return enumConstant;
            }
        }
        return null;
    }
    static <T extends Enum<T> & IValueEnum> List<Integer> getSystemFieldIds(Class<T> enumClass) {
        return Arrays.stream(enumClass.getEnumConstants())
            .map(IValueEnum::getValue)
            .toList();
    }
    static <T extends Enum<T> & IValueEnum> boolean IsSystemField(Class<T> enumClass, Integer fieldId) {
        return fieldId != null && Arrays.stream(enumClass.getEnumConstants()).anyMatch(e -> e.getValue() == fieldId);
    }
}
