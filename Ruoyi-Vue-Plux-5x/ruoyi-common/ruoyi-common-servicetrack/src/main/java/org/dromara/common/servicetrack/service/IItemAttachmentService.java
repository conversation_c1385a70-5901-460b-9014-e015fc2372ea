package org.dromara.common.servicetrack.service;


import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;

import java.util.Collection;
import java.util.List;

/**
 * item 附件管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IItemAttachmentService {

    /**
     * 查询item附件列表
     *
     * @param projectId 项目id
     * @param itemId    item id
     * @return item附件集合
     */
    List<ItemAttachmentVo> selectItemAttachmentList( Integer projectId, Integer itemId);
}
