package org.dromara.common.servicetrack.domain.bo;

import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.NotificationRecipientEmailAddress;
import org.dromara.common.servicetrack.domain.NotificationRuleCondition;

/**
 * notification_rule_condition表的bo类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NotificationRuleCondition.class)
public class NotificationRuleConditionBo extends STBaseEntity {
    /**
     * key_id
     */
    private Long id;

    /**
     * project_id
     */
    private Integer projectId;

    /**
     * rule_id
     */
    private Integer ruleId;

    /**
     * condition_id
     */
    private Integer conditionId;

    /**
     * field_id
     */
    private Integer fieldId;

    /**
     * operator
     */
    private Integer operator;

    /**
     * field_value_id
     */
    private Integer fieldValueId;

    /**
     * field_value
     */
    private String fieldValue;
}
