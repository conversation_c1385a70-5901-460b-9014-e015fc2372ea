package org.dromara.common.servicetrack.service.impl;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mail.config.properties.MailConfigDo;
import org.dromara.common.mail.factory.MailAccountFactory;
import org.dromara.common.servicetrack.constant.eProjectSetting;
import org.dromara.common.servicetrack.mapper.ProjectInfoMapper;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.extra.mail.MailAccount;
import java.util.concurrent.ConcurrentHashMap;
import java.util.*;

@Slf4j
@Component
public class MailAccountCache {

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    private final Map<Integer, MailAccount> cache = new ConcurrentHashMap<>();

    private MailAccountFactory mailAccountFactory;

    @PostConstruct
    public void init() {
        this.mailAccountFactory = SpringUtils.getBean(MailAccountFactory.class);
        // 可选：初始化时加载所有项目的配置
        // preloadAll();
    }

    /**
     * 获取指定项目的 MailAccount
     */
    public MailAccount get(Integer projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("Project ID cannot be null");
        }

        MailAccount account = cache.get(projectId);
        if (account == null) {
            synchronized (this) {
                account = cache.get(projectId);
                if (account == null) {
                    account = loadAndCacheMailAccount(projectId);
                }
            }
        }
        return account;
    }

    private MailAccount loadAndCacheMailAccount(Integer projectId) {
        try {
            String emailSetting = projectInfoMapper.getEmailSetting(
                projectId, eProjectSetting.Project_Email_Setting.getValue()
            );
            if (emailSetting != null && !emailSetting.isEmpty()) {
                MailConfigDo mailConfig = JsonUtils.parseObject(emailSetting, MailConfigDo.class);
                if (mailConfig != null) {
                    try {
                        var encodePwd = mailConfig.getPass();
                        mailConfig.setPass(ValueConvert.decodeBase64(encodePwd));
                    }
                    catch (Exception e){
                        log.error("邮件配置信息password 解密失败",e);
                        mailConfig.setPass(mailConfig.getPass());
                    }

                    MailAccount account = mailAccountFactory.createMailAccount(mailConfig);
                    cache.put(projectId, account);
                    return account;
                }
            }
        } catch (Exception e) {
            log.error("加载项目 {} 邮件配置失败", projectId, e);
            // 可选择抛异常或返回 null，取决于业务场景
        }
        return null;
    }

    /**
     * 移除指定项目的缓存
     */
    public void remove(Integer projectId) {
        cache.remove(projectId);
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        cache.clear();
    }
}
