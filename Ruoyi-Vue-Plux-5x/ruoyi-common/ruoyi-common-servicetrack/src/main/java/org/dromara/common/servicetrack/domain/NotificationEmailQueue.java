package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;

/**
 * 对象 notification_email_queue
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("notification_email_queue")
public class NotificationEmailQueue extends STBaseEntity {
    /**
     * key_id
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * queue_id
     */
    @TableField(value = "queue_id")
    private Integer queueId;

    /**
     * project_id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * rule_id
     */
    @TableField(value = "rule_id")
    private Integer ruleId;

    /**
     * object_type
     */
    @TableField(value = "object_type")
    private Integer objectType;

    /**
     * object_id
     */
    @TableField(value = "object_id")
    private Integer objectId;

    /**
     * created_time
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * email_subject
     */
    @TableField(value = "email_subject")
    private String emailSubject;

    /**
     * email_body
     */
    @TableField(value = "email_body")
    private String emailBody;

    /*
     * status： 1=PENDING, 2=SENDING, 3=SENT, 4=FAILED
     */
    @TableField(value = "status")
    private Integer status;

    /*
     * retry_count
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /*
     * max_retry
     */
    @TableField(value = "max_retry")
    private Integer maxRetry;

    /*
     * recipient's email address
     */
    @TableField(value = "recipient")
    private String recipient;

    /*
     * defined recipient's email address using cc
     */
    @TableField(value = "recipient_email_address")
    private String recipientEmailAddress;
}
