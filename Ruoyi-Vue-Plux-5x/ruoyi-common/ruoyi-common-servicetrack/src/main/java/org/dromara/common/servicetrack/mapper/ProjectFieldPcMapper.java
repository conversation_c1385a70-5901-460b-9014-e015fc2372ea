package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectFieldPc;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldPcBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcVo;

import java.util.List;

/**
 * 项目字段父子关系 数据层
 *
 * <AUTHOR>
 */
public interface ProjectFieldPcMapper extends BaseMapperPlus<ProjectFieldPc, ProjectFieldPcVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectFieldPc> buildWrapper(ProjectFieldPcBo bo) {
        LambdaQueryWrapper<ProjectFieldPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectFieldPc::getProjectId, bo.getProjectId());
        lqw.eq(bo.getParentFieldId() != null, ProjectFieldPc::getParentFieldId, bo.getParentFieldId());
        lqw.eq(bo.getChildFieldId() != null, ProjectFieldPc::getChildFieldId, bo.getChildFieldId());
        return lqw;
    }

    /**
     * 查询项目字段父子关系列表
     *
     * @param projectId 项目ID
     * @param parentFieldId 父字段ID
     * @return 项目字段父子关系列表
     */
    List<ProjectFieldPcVo> selectFieldPcList(@Param("projectId") Integer projectId, 
                                            @Param("parentFieldId") Integer parentFieldId);

    /**
     * 查询子字段列表
     *
     * @param projectId 项目ID
     * @param parentFieldId 父字段ID
     * @return 子字段列表
     */
    List<ProjectFieldPcVo> selectChildFieldList(@Param("projectId") Integer projectId, 
                                               @Param("parentFieldId") Integer parentFieldId);

    /**
     * 查询父字段列表
     *
     * @param projectId 项目ID
     * @param childFieldId 子字段ID
     * @return 父字段列表
     */
    List<ProjectFieldPcVo> selectParentFieldList(@Param("projectId") Integer projectId, 
                                                @Param("childFieldId") Integer childFieldId);

    /**
     * 批量删除项目字段父子关系
     *
     * @param projectId 项目ID
     * @param fieldIds 字段ID列表
     */
    void deleteFieldPcByFieldIds(@Param("projectId") Integer projectId, 
                                @Param("fieldIds") List<Integer> fieldIds);
}
