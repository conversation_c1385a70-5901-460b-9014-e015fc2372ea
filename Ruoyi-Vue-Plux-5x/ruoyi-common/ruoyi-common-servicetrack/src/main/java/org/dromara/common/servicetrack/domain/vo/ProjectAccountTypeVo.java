package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectAccountType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目账户类型视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectAccountType.class)
public class ProjectAccountTypeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * 项目ID
     */
    private Integer projectId;
    /**
     * 账户类型ID
     */
    private Integer accountTypeId;
    /**
     * 类型Id
     */
    private Integer typeId;
    /**
     * 账户类型名称
     */
    private String accounttypeName;
}
