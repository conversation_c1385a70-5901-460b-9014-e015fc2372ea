package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.utils.ValueConvert;
import java.util.Objects;

public class CheckBoxFieldValue extends BaseFieldValue {
    private Boolean rawValue;

    @Override
    public Boolean equalsTo(IFieldValue other) {
        return equals2((CheckBoxFieldValue) other);
    }

    private Boolean equals2(CheckBoxFieldValue other) {
        if (other == null) {
            return false;
        } else {
            return Objects.equals(rawValue, other.rawValue);
        }
    }

    @Override
    public String getDisplayValue() {
        int nValue = -1;
        if (rawValue != null) {
            nValue = rawValue ? 1 : 0;
        }

        return String.format("%d", nValue);
    }

    @Override
    public Object getRawValue() {
        return rawValue != null ? (rawValue ? 1 : 0) : null;
    }

    @Override
    public void readValueFromDB(Object data) {
        if (ValueConvert.isNull(data)) {
            rawValue = null;
        } else {
            rawValue = ValueConvert.readInt(data) == 1;
        }
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if (value == null) {
            rawValue = null;
        } else {
            String str = value.toString();
            if (str.isEmpty()) {
                rawValue = null;
            } else {
                if (str.equalsIgnoreCase("true") || str.equals("1") || str.equalsIgnoreCase("on")) {
                    rawValue = true;
                } else {
                    rawValue = false;
                }
            }
        }
    }

    @Override
    public String toCustomFieldFormatString() {
        if (rawValue != null) {
            return rawValue ? "1" : "0";
        } else {
            return null;
        }
    }

    @Override
    public Boolean isUnassigned() {
        return rawValue == null;
    }
}
