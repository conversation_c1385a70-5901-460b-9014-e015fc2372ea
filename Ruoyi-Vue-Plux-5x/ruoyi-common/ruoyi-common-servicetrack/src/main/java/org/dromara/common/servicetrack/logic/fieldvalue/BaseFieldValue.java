package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;

/**
 * Base Field Value
 */
public abstract class BaseFieldValue implements IFieldValue {

    public abstract Boolean equalsTo(IFieldValue other);

    public abstract String getDisplayValue();

    public abstract Object getRawValue();

    public abstract void readValueFromDB(Object data);

    public abstract void setFieldValue(Object value, Integer option);

    public abstract String toCustomFieldFormatString();

    public abstract Boolean isUnassigned();

    @Override
    public  Object getValueObject()
    {
        return getRawValue();
    }
    @Override
    public Object getAdditionalInfo()
    {
        return null;
    }
}

