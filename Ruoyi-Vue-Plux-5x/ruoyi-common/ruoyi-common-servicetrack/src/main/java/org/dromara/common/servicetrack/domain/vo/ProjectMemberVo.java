package org.dromara.common.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.ProjectMember;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目视图对象 project_member
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectMember.class)
public class ProjectMemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * user id
     */
    private Integer userId;

    /**
     * user name
     */
    private String userName;

    /**
     * nick name
     */
    private String nickName;

    /**
     * user type
     **/
    private Integer userType;

    /**
     * user type name
     */
    private String userTypeName;

    /**
     * account type id
     **/
    private Integer accountTypeId;

    /**
     * account type name
     */
    private String accountTypeName;
}
