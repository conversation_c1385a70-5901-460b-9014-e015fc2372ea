package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.utils.ValueConvert;

import java.util.Objects;

/**
 * 文本框字段值
 */
public class TextBoxFieldValue extends BaseFieldValue {
    private String rawValue;

    @Override
    public Object getRawValue() {
        return rawValue;
    }

    @Override
    public String getDisplayValue() {
        return rawValue;
    }

    @Override
    public void readValueFromDB(Object data) {
        rawValue = ValueConvert.isNull(data) ? null : ValueConvert.readString(data);
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if (value == null) {
            rawValue = null;
        } else {
            rawValue = value.toString();
        }
    }

    @Override
    public Boolean equalsTo(IFieldValue other) {
        return equals2((TextBoxFieldValue) other);
    }

    @Override
    public String toCustomFieldFormatString() {
        return rawValue;
    }

    private Boolean equals2(TextBoxFieldValue other) {
        if (other == null) {
            return false;
        }
        return Objects.equals(rawValue, other.rawValue);
    }

    public Boolean IsInvalid() {
        return false;
    }

    @Override
    public Boolean isUnassigned() {
        return rawValue == null || rawValue.isEmpty();
    }

}
