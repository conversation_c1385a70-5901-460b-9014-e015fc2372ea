package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 项目字段父子选择项关系对象 project_field_pc_selection
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_pc_selection")
public class ProjectFieldPcSelection extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 父字段ID
     */
    @TableField(value = "parent_field_id")
    private Integer parentFieldId;

    /**
     * 子字段ID
     */
    @TableField(value = "child_field_id")
    private Integer childFieldId;

    /**
     * 父选择项ID
     */
    @TableField(value = "parent_choice_id")
    private Integer parentChoiceId;

    /**
     * 子选择项ID
     */
    @TableField(value = "child_choice_id")
    private Integer childChoiceId;
}
