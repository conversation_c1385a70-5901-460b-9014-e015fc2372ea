package org.dromara.common.servicetrack.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.NotificationEmailTemplate;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通知邮件模板视图对象 notification_email_template
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = NotificationEmailTemplate.class)
public class NotificationEmailTemplateVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 模板ID
     */
    private Integer templateId;

    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 邮件主题
     */
    private String emailSubject;

    /**
     * 邮件内容
     */
    private String emailBody;
}
