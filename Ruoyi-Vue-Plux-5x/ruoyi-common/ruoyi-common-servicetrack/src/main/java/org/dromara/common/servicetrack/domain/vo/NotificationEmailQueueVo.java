package org.dromara.common.servicetrack.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.NotificationEmailQueue;

/**
 * notification_email_queue表的vo类
 */
@Data
@AutoMapper(target = NotificationEmailQueue.class)
public class NotificationEmailQueueVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * key_id
     */
    private Long id;

    /**
     * queue_id
     */
    private Integer queueId;

    /**
     * project_id
     */
    private Integer projectId;

    /**
     * rule_id
     */
    private Integer ruleId;

    /**
     * object_type
     */
    private Integer objectType;

    /**
     * object_id
     */
    private Integer objectId;

    /**
     * created_time
     */
    private Date createdTime;

    /**
     * email_subject
     */
    private String emailSubject;

    /**
     * email_body
     */
    private String emailBody;

    /*
     * status： 1=PENDING, 2=SENDING, 3=SENT, 4=FAILED
    */
    private Integer status;

    /*
     * retry_count
    */
    private Integer retryCount;

    /*
     * max_retry
    */
    private Integer maxRetry;

    /*
     * recipient's email address
     */
     private String recipient;

    /*
     * defined recipient's email address using cc
     */
    private String recipientEmailAddress;
}
