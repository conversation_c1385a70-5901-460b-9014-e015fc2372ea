package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldCustomBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldCustomVo;

/**
 * 项目自定义字段 数据层
 *
 * <AUTHOR>
 */
public interface ProjectFieldCustomMapper extends BaseMapperPlus<ProjectFieldCustom, ProjectFieldCustomVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectFieldCustom> buildWrapper(ProjectFieldCustomBo bo) {
        LambdaQueryWrapper<ProjectFieldCustom> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectFieldCustom::getProjectId, bo.getProjectId());
        lqw.eq(bo.getFieldId() != null, ProjectFieldCustom::getFieldId, bo.getFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getFieldName()), ProjectFieldCustom::getFieldName, bo.getFieldName());
        lqw.eq(bo.getModuleId() != null, ProjectFieldCustom::getModuleId, bo.getModuleId());
        lqw.eq(bo.getFieldType() != null, ProjectFieldCustom::getFieldType, bo.getFieldType());
        return lqw;
    }
}