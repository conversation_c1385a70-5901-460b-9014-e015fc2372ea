package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectFieldPc;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目字段父子关系视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectFieldPc.class)
public class ProjectFieldPcVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 父字段ID
     */
    private Integer parentFieldId;

    /**
     * 子字段ID
     */
    private Integer childFieldId;
}
