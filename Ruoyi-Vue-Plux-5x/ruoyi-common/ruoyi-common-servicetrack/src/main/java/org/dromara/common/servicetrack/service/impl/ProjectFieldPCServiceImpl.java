package org.dromara.common.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.common.servicetrack.domain.ProjectFieldPc;
import org.dromara.common.servicetrack.domain.ProjectFieldPcSelection;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldPCBinderBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcSelectionModelVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcSelectionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcVo;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectFieldPcMapper;
import org.dromara.common.servicetrack.mapper.ProjectFieldPcSelectionMapper;
import org.dromara.common.servicetrack.service.IProjectFieldPCService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目字段父子关系 服务层
 *
 * <AUTHOR> fei
 * */
@Service
@RequiredArgsConstructor
public class ProjectFieldPCServiceImpl implements IProjectFieldPCService {
    private final ProjectFieldPcMapper projectFieldPcMapper;
    private final ProjectFieldPcSelectionMapper projectFieldPcSelectionMapper;
    @Override
    public List<ProjectFieldPcVo> selectFieldPcList(Integer projectId) {
        return projectFieldPcMapper.selectFieldPcList(projectId,null);
    }

    @Override
    public List<ProjectFieldPcSelectionVo> selectFieldPcSelectionList(Integer projectId) {
        return projectFieldPcSelectionMapper.selectFieldPcSelectionList(projectId,null,null);
    }

    @Override
    public ProjectFieldPcSelectionModelVo getOneFieldPCSelection(Integer projectId, Integer parentFieldId, Integer childFieldId) {
        if(projectId == null || projectId == 0 ||
            parentFieldId == null || parentFieldId == 0 ||
            childFieldId == null || childFieldId == 0){
            throw new IllegalArgumentException("projectId, parentFieldId, childFieldId can't be null or 0.");
        }
        ProjectFieldPcSelectionModelVo model = new ProjectFieldPcSelectionModelVo();
        model.setProjectId(projectId);
        model.setParentFieldId(parentFieldId);
        model.setChildFieldId(childFieldId);
        var selectionList = projectFieldPcSelectionMapper.selectFieldPcSelectionList(projectId, parentFieldId, childFieldId);
        if( !selectionList.isEmpty()){
            model.setSelections(new ArrayList<>());
           //group by parent choice id
           var groupedSelectionList = selectionList.stream().collect(Collectors.groupingBy(ProjectFieldPcSelectionVo::getParentChoiceId));
           for(var entry : groupedSelectionList.entrySet()){
              var parentChoiceId = entry.getKey();
              var childSelectionList = entry.getValue();
              var selection = new ProjectFieldPcSelectionModelVo.ProjectFieldPCSelectionInfoBo();
              selection.setParentChoiceId(parentChoiceId);
              selection.setParentChoiceName(ProjectManager.getInstance(projectId).getChoiceName(parentFieldId, parentChoiceId));
              selection.setChildChoices(new ArrayList<>());
              for(var childSelection : childSelectionList){
                  var childChoice = new ProjectFieldPcSelectionModelVo.SelectionChoiceInfoBo();
                  childChoice.setChoiceId(childSelection.getChildChoiceId());
                  childChoice.setChoiceName(ProjectManager.getInstance(projectId).getChoiceName(childFieldId, childSelection.getChildChoiceId()));
                  selection.getChildChoices().add(childChoice);
              }
              model.getSelections().add(selection);
           }
        }
        return model;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ProjectFieldPCBinderBo bo) {
        if( bo == null){
            throw new IllegalArgumentException("projectFieldPCBinderBo can't be null.");
        }
        if( bo.getProjectId() == null || bo.getProjectId() == 0){
            throw new IllegalArgumentException("projectId can't be null or 0.");
        }
        if( bo.getParentFieldId() == null || bo.getParentFieldId() == 0){
            throw new IllegalArgumentException("parentFieldId can't be null or 0.");
        }
        if( bo.getChildFieldId() == null || bo.getChildFieldId() == 0){
            throw new IllegalArgumentException("childFieldId can't be null or 0.");
        }
        var existedInPC = false;
        if( bo.getId() == null || bo.getId() == 0){
            //need to check if the parent and child field is valid
            existedInPC = projectFieldPcMapper.exists(new LambdaQueryWrapper<ProjectFieldPc>()
                .eq(ProjectFieldPc::getProjectId, bo.getProjectId())
                .eq(ProjectFieldPc::getParentFieldId, bo.getParentFieldId())
                .eq(ProjectFieldPc::getChildFieldId, bo.getChildFieldId()));
        }
        if( !existedInPC ){
            ProjectFieldPc pc = new ProjectFieldPc();
            pc.setProjectId(bo.getProjectId());
            pc.setParentFieldId(bo.getParentFieldId());
            pc.setChildFieldId(bo.getChildFieldId());
            projectFieldPcMapper.insert(pc);
            bo.setId(pc.getId());
        }
        //delete all existed selection
        projectFieldPcSelectionMapper.delete(new LambdaQueryWrapper<ProjectFieldPcSelection>()
            .eq(ProjectFieldPcSelection::getProjectId, bo.getProjectId())
            .eq(ProjectFieldPcSelection::getParentFieldId, bo.getParentFieldId())
            .eq(ProjectFieldPcSelection::getChildFieldId, bo.getChildFieldId()));

        //insert new selections
        if(bo.getSelections() != null && !bo.getSelections().isEmpty()){
            List<ProjectFieldPcSelection> list = new ArrayList<>();
            for(var selection:bo.getSelections()){
                for(var childChoiceId:selection.getChildChoiceIds()){
                    ProjectFieldPcSelection oneItemSelection = new ProjectFieldPcSelection();
                    oneItemSelection.setProjectId(bo.getProjectId());
                    oneItemSelection.setParentFieldId(bo.getParentFieldId());
                    oneItemSelection.setChildFieldId(bo.getChildFieldId());
                    oneItemSelection.setParentChoiceId(selection.getParentChoiceId());
                    oneItemSelection.setChildChoiceId(childChoiceId);
                    list.add(oneItemSelection);
                }
            }
            projectFieldPcSelectionMapper.insertBatch(list);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValid(Integer projectId, Integer parentFieldId, Integer childFieldId, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return projectFieldPcMapper.delete(new LambdaQueryWrapper<ProjectFieldPc>()
            .eq(ProjectFieldPc::getProjectId, projectId)
            .eq(ProjectFieldPc::getParentFieldId, parentFieldId)
            .eq(ProjectFieldPc::getChildFieldId, childFieldId)) > 0 &&
            projectFieldPcSelectionMapper.delete(new LambdaQueryWrapper<ProjectFieldPcSelection>()
                .eq(ProjectFieldPcSelection::getProjectId, projectId)
                .eq(ProjectFieldPcSelection::getParentFieldId, parentFieldId)
                .eq(ProjectFieldPcSelection::getChildFieldId, childFieldId)) > 0;
    }
}
