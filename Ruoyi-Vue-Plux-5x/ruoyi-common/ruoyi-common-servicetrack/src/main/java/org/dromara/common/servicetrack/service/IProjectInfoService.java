package org.dromara.common.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.ProjectInfoVo;
import org.dromara.common.servicetrack.domain.vo.ProjectSettingInCacheVo;


import java.util.Collection;
import java.util.List;

/**
 * 项目管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IProjectInfoService {

    /**
     * 查询项目
     */
    ProjectInfoVo queryById(Long id);

    /**
     * 查询项目列表
     */
    TableDataInfo<ProjectInfoVo> queryPageList(PageQuery pageQuery);

    /**
     * 查询项目列表
     */
    List<ProjectInfoVo> queryList(ProjectInfoBo bo);
    List<ProjectInfoVo> queryListByUser(Integer getOpenIncidentCount);

    /**
     * 根据项目id查询项目信息
     */
    ProjectInfoVo selectProjectInfoById(Integer projectId);
    /**
     * 新增项目
     */
    Integer insertByBo(ProjectInfoBo bo);

    /**
     * 修改项目
     */
    Integer updateByBo(ProjectInfoBo bo);

    /**
     * 校验并批量删除项目信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /*
    * 获取项目邮箱设置
     */
    String getEmailSetting(Integer projectId,Integer settingId);

    /*
     * 获取工作项目列表
     */
    List<Integer> getWorkProjectIds(Integer baseProjectId);

    /*
     * 获取项目设置列表
     */
    List<ProjectSettingInCacheVo> getProjectSettingList(Integer projectId);
}
