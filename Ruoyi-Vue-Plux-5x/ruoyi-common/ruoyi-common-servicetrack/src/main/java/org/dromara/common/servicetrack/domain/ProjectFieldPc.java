package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 项目字段父子关系对象 project_field_pc
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_field_pc")
public class ProjectFieldPc extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 父字段ID
     */
    @TableField(value = "parent_field_id")
    private Integer parentFieldId;

    /**
     * 子字段ID
     */
    @TableField(value = "child_field_id")
    private Integer childFieldId;
}
