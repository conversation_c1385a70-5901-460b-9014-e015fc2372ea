package org.dromara.common.servicetrack.constant;

public enum eUserTypeDef implements IValueEnum{
    IndividualUser(1),
    AccountType(2),
    Group(3);
    private final int value;

    /*
     *构造函数在定义常量时自动调用
     */
    eUserTypeDef(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }
    public static eUserTypeDef from(Integer value) {
        return IValueEnum.valueOf(eUserTypeDef.class, value);
    }
}
