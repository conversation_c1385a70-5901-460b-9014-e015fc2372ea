package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectFieldPCBinderBo extends STBaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 父字段ID
     */
    @NotNull(message = "父字段ID不能为空")
    private Integer parentFieldId;


    @NotNull(message = "子字段ID不能为空")
    private Integer childFieldId;

    /**
     * 选择项列表
     */
    private List<ProjectFieldPCSelectionInfoBo> selections;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectFieldPCSelectionInfoBo extends STBaseEntity{
        /**
         * 父选择项ID
         */
        @NotNull(message = "父选择项ID不能为空")
        private Integer parentChoiceId;

        /**
         * 子选择项ID
         */
        @NotNull(message = "子选择项不能为空")
        private List<Integer> childChoiceIds;
    }
}
