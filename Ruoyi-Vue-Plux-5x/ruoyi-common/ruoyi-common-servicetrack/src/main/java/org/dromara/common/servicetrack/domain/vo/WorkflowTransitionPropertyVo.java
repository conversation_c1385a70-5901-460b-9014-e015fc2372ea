package org.dromara.common.servicetrack.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 工作流转换属性VO
 *
 * <AUTHOR> fei
 */
@Data
public class WorkflowTransitionPropertyVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private  Long id;

    private Integer transitionId;

    private String transitionName;

    private String transitionIdentifier;

    private String transitionDescription;

    private List<WorkflowTransitionFieldVo> fields;
    private List<WorkflowTransitionPermissionVo> permissions;

    public WorkflowTransitionPropertyVo(WorkflowTransitionVo vo) {
        this.id = vo.getId();
        this.transitionId = vo.getTransitionId();
        this.transitionName = vo.getTransitionName();
        this.transitionIdentifier = vo.getTransitionIdentifier();
        this.transitionDescription = vo.getTransitionDescription();
    }
}
