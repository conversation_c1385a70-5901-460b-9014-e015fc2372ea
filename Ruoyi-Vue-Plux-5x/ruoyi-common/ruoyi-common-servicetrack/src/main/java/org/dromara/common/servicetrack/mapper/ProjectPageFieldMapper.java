package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectPageField;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;


import java.util.List;

/**
 * 页面字段管理 数据层
 *
 * <AUTHOR> fei
 */
public interface ProjectPageFieldMapper extends BaseMapperPlus<ProjectPageField, ProjectPageFieldVo>  {


    @DataPermission({
        @DataColumn(key = "pageName", value = "f.page_id"),
        @DataColumn(key = "fieldName", value = "f.field_id")
    })
    List<ProjectPageFieldVo> selectPageFieldList(@Param(Constants.WRAPPER) Wrapper<ProjectPageField> queryWrapper);
}
