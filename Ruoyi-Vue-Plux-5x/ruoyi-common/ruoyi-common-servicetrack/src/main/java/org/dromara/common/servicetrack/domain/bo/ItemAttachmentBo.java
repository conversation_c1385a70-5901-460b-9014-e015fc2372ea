package org.dromara.common.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ItemAttachment;
import org.dromara.common.servicetrack.domain.ProjectInfo;

import java.util.Date;

/**
 * Item附件业务对象 item_attachment
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemAttachment.class)
public class ItemAttachmentBo extends  STBaseEntity{
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * item id
     */
    @NotNull(message = "item id不能为空")
    private Integer itemId;

    /**
     * 附件Id
     */
    @NotNull(message = "附件Id不能为空")
    private Integer attachmentId;

    /**
     * 关联的OSS ID
     */
    private Long ossId;
    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件类型
     */
    private Integer typeId;
    /**
     * 附件状态
     */
    @TableField(value = "state_id")
    private Integer stateId;

    /**
     * 上传创建时间
     */
    private Date createdTime;

    /**
     * 上传创建人
     */
    private Integer createdBy;
}
