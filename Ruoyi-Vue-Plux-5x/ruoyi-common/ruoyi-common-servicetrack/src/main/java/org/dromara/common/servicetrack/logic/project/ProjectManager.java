package org.dromara.common.servicetrack.logic.project;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.mail.MailAccount;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.util.validation.metadata.DatabaseException;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mail.utils.STMailUtils;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.service.IProjectDataService;
import org.dromara.common.servicetrack.service.impl.ProjectDataServiceImpl;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 项目管理器
 */
@Slf4j
//@Component
public class ProjectManager {
    private final Integer projectId;
    private final IProjectDataService dataService;



    // 静态内部类持有所有静态资源，实现懒加载
    private static class LazyHolder {
        private static final Map<Integer, ProjectManager> instances = new ConcurrentHashMap<>();
        private static final Map<Integer, ProjectInfoVo> projectInfoMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<ProjectPageFieldVo>> projectPageFieldMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<ProjectFieldSelectionVo>> projectFieldSelectionMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<WorkflowStateVo>> workflowStateMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<WorkflowTransitionVo>> workflowTransitionMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<ProjectMemberVo>> projectMemberMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<SysUserSimpleInfoVo>> sysUserMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<ProjectItemTypeVo>> projectItemTypeMap = new ConcurrentHashMap<>();
        private static final Map<Integer, HashMap<Long, String>> sysDeptNameMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<NotificationRuleListItemVo>> projectNotificationRuleMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<CustomerSimpleInfoVo>> customerMap = new ConcurrentHashMap<>();
        private static final Map<Integer, List<ProjectAccountPermissionVo>> projectAccountPermissionMap = new ConcurrentHashMap<>();
        private static final Set<Integer> initializedProjects = ConcurrentHashMap.newKeySet();
        private static final Object lock = new Object();
    }

    private ProjectManager(Integer projectId) {
        this.projectId = projectId;
        this.dataService = new ProjectDataServiceImpl();
    }
    /**
     * 获取ProjectManager实例并确保指定项目的数据已初始化
     * @param projectId 项目ID
     * @return ProjectManager实例
     * @throws ServiceException 当项目初始化失败时抛出
     */
    public static ProjectManager getInstance(Integer projectId) throws ServiceException {
        ProjectManager instance = LazyHolder.instances.get(projectId);
        if (instance == null) {
            synchronized (LazyHolder.lock) {
                instance = LazyHolder.instances.get(projectId);
                if (instance == null) {
                    instance = new ProjectManager(projectId);
                    instance.ensureProjectInitialized(projectId);
                    LazyHolder.instances.put(projectId, instance);
                }
            }
        }
        return instance;
    }
    /**
     * 确保项目数据已初始化
     */
    private void ensureProjectInitialized(Integer projectId) throws ServiceException {
        if (projectId == null) {
            throw new IllegalArgumentException("Project ID cannot be null");
        }

        // 如果项目未初始化，则进行初始化
        if (!LazyHolder.initializedProjects.contains(projectId)) {
            synchronized (LazyHolder.lock) {
                // 双重检查，防止并发初始化
                if (!LazyHolder.initializedProjects.contains(projectId)) {
                    try {
                        //set system project id :-100,just store some system level data, as system user
                        if(projectId.equals(stConstant.System_Project_Id)){
                            ProjectInfoVo projectInfo = new ProjectInfoVo();
                            projectInfo.setProjectId(projectId);
                            projectInfo.setProjectName("system");
                            projectInfo.setProjectKey("system");
                            projectInfo.setBaseProjectId(projectId);
                            LazyHolder.projectInfoMap.put(projectId, projectInfo);

                            //get system user list
                            List<SysUserSimpleInfoVo> members = dataService.selectSysUserList();
                            if( members != null && !members.isEmpty())
                                LazyHolder.sysUserMap.put(projectId, members);

                            //initialize system dept name map firstly.
                            LazyHolder.sysDeptNameMap.put(projectId, LazyHolder.sysDeptNameMap.getOrDefault(projectId, new HashMap<>()));

                            // 标记项目已初始化
                            LazyHolder.initializedProjects.add(projectId);
                            log.info("system level data initialized successfully");
                            return;
                        }
                        // 从数据库加载项目数据
                        ProjectInfoVo projectInfo = dataService.selectProjectInfoById(projectId);
                        if (projectInfo == null) {
                            throw new ServiceException("Project " + projectId + " not found in database");
                        }
                        ProjectPageFieldBo projectPageFieldBo = new ProjectPageFieldBo();
                        projectPageFieldBo.setProjectId(projectId);
                        List<ProjectPageFieldVo> projectPageFields = dataService.selectPageFieldList(projectPageFieldBo);

                        ProjectFieldSelectionBo projectFieldSelectionBo = new ProjectFieldSelectionBo();
                        projectFieldSelectionBo.setProjectId(projectId);
                        List<ProjectFieldSelectionVo> projectFieldSelections = dataService.selectFieldSelctionList(projectFieldSelectionBo);

                        List<ProjectMemberVo> members = dataService.selectMemberList(projectId);
                        if( members != null && !members.isEmpty())
                            LazyHolder.projectMemberMap.put(projectId, members);

                        WorkflowStateBo workflowStateBo = new WorkflowStateBo();
                        workflowStateBo.setProjectId(projectId);
                        var workflowStateVoList = dataService.selectStateList(workflowStateBo);

                        WorkflowTransitionBo workflowTransitionBo = new WorkflowTransitionBo();
                        workflowTransitionBo.setProjectId(projectId);
                        var workflowTransitionVoList = dataService.selectTransitionList(workflowTransitionBo);

                        ProjectItemTypeBo projectItemTypeBo = new ProjectItemTypeBo();
                        projectItemTypeBo.setProjectId(projectId);
                        var projectItemTypeVoList = dataService.selectItemTypeList(projectId);

                        var notificationRuleList = dataService.selectNotificationRuleList(projectId);

                        //just for base project to get customer list
                        var customerList = projectInfo.getProjectType() == eProjectType.BASE_PROJECT.getValue() ? dataService.selectCustomerSimpleInfoList(projectId): null;

                        var accountPermissionList = dataService.selectAccountPermissionList(projectId);
//                        // 存储项目数据
                        LazyHolder.projectInfoMap.put(projectId, projectInfo);
                        if (!projectPageFields.isEmpty()) {
                            LazyHolder.projectPageFieldMap.put(projectId, projectPageFields);
                        }
                        if (!projectFieldSelections.isEmpty()) {
                            LazyHolder.projectFieldSelectionMap.put(projectId, projectFieldSelections);
                        }
                        if (!workflowStateVoList.isEmpty()) {
                            LazyHolder.workflowStateMap.put(projectId, workflowStateVoList);
                        }
                        if (!workflowTransitionVoList.isEmpty()) {
                            LazyHolder.workflowTransitionMap.put(projectId, workflowTransitionVoList);
                        }
                        if (!projectItemTypeVoList.isEmpty()) {
                            LazyHolder.projectItemTypeMap.put(projectId, projectItemTypeVoList);
                        }
                        if (!notificationRuleList.isEmpty()) {
                            LazyHolder.projectNotificationRuleMap.put(projectId, notificationRuleList);
                        }
                        if( customerList != null && !customerList.isEmpty()){
                            LazyHolder.customerMap.put(projectId, customerList);
                        }
                        if( accountPermissionList != null && !accountPermissionList.isEmpty()){
                            LazyHolder.projectAccountPermissionMap.put(projectId, accountPermissionList);
                        }
                        // 标记项目已初始化
                        LazyHolder.initializedProjects.add(projectId);

                        log.info("Project {} initialized successfully", projectId);
                    } catch (Exception e) {
                        String errorMessage = "Failed to initialize project " + projectId;
                        log.error(errorMessage, e);
                       // throw new ServiceException(errorMessage +". Error Message:" + e.getMessage());
                    }
                }
            }
        }
    }
    /**
     * 重置项目数据 - 移除现有数据，后续访问时会触发重新加载
     * @param projectId 需要重置的项目ID
     */
    public void reset(Integer projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("Project ID cannot be null");
        }

        synchronized (LazyHolder.lock) {
            // 检查项目是否已初始化
            if (projectId == 0) {
                // 重置所有项目数据
                LazyHolder.projectInfoMap.clear();
                LazyHolder.projectPageFieldMap.clear();
                LazyHolder.projectFieldSelectionMap.clear();
                LazyHolder.workflowStateMap.clear();
                LazyHolder.workflowTransitionMap.clear();
                LazyHolder.projectMemberMap.clear();
                LazyHolder.sysUserMap.clear();
                LazyHolder.sysDeptNameMap.clear();
                LazyHolder.projectItemTypeMap.clear();
                LazyHolder.projectNotificationRuleMap.clear();
                LazyHolder.customerMap.clear();
                LazyHolder.initializedProjects.clear();

                log.info("All project data cleared and ready for reload");
            } else {
                if (!LazyHolder.initializedProjects.contains(projectId)) {
                   log.error("Project {} was not initialized, nothing to reset", projectId);
                   return;
                }
                // 移除指定项目的相关数据
                LazyHolder.projectInfoMap.remove(projectId);
                LazyHolder.projectPageFieldMap.remove(projectId);
                LazyHolder.projectFieldSelectionMap.remove(projectId);
                LazyHolder.workflowStateMap.remove(projectId);
                LazyHolder.workflowTransitionMap.remove(projectId);
                LazyHolder.projectMemberMap.remove(projectId);
                LazyHolder.sysUserMap.remove(projectId);
                LazyHolder.sysDeptNameMap.remove(projectId);
                LazyHolder.projectItemTypeMap.remove(projectId);
                LazyHolder.projectNotificationRuleMap.remove(projectId);
                LazyHolder.customerMap.remove(projectId);
                LazyHolder.initializedProjects.remove(projectId);

                log.info("Project {} data cleared and ready for reload", projectId);
            }

        }
    }
    public List<Integer> getWorkProjectIds(){
      if( !isBaseProject())
          return new ArrayList<>(this.projectId);
      var projectInfo = getProjectInfo();
      if( projectInfo == null)
          return new ArrayList<>();

        List<Integer> workProjectIds = projectInfo.getWorkProjectIds() == null ? new ArrayList<>():projectInfo.getWorkProjectIds();
        if( !workProjectIds.isEmpty())
            return workProjectIds;


        workProjectIds = dataService.getWorkProjectIds(this.projectId);
        synchronized (LazyHolder.lock) {
            projectInfo.setWorkProjectIds(workProjectIds);
            LazyHolder.projectInfoMap.put(this.projectId, projectInfo);
        }

        return workProjectIds;
    }
    // 获取项目数据的方法
    public ProjectInfoVo getProjectInfo() {
        validateProjectInitialized(this.projectId);
        return LazyHolder.projectInfoMap.get(this.projectId);
    }
    public Integer getBaseProjectId(){
        var projectInfo = getProjectInfo();
        if( projectInfo == null)
            return 0;
        var baseProjectId =  projectInfo.getBaseProjectId();
        if( baseProjectId == null || baseProjectId == 0)
            baseProjectId =  this.projectId;
        return baseProjectId;
    }
    public  Boolean isBaseProject(){
        var projectInfo = getProjectInfo();
        if( projectInfo == null)
            return false;
        return projectInfo.getProjectType() == eProjectType.BASE_PROJECT.getValue();
    }
    public List<ProjectMemberVo> getProjectMembers() {
        validateProjectInitialized(this.projectId);
        return LazyHolder.projectMemberMap.getOrDefault(this.projectId, new ArrayList<>());
    }
    public void setProjectMembers(List<ProjectMemberVo> members){
        validateProjectInitialized(this.projectId);
        synchronized (LazyHolder.lock) {
            LazyHolder.projectMemberMap.remove(this.projectId);
            LazyHolder.projectMemberMap.put(this.projectId, members);
        }
    }
    public String getProjectMemberNickName(Integer memberId) {
        var memberList = getProjectMembers();
        var nickname = "";
        for (var member : memberList) {
            if (member.getUserId().equals(memberId)) {
                nickname = member.getNickName();
                break;
            }
        }
        if(Objects.equals(nickname, "")){
            var sysUserNickName =  getSysUserNickName(memberId);
            if( !Objects.equals(sysUserNickName, ""))
                return String.format("(%s)", sysUserNickName);
        }
        return nickname;
    }
    public Integer getAccountTypeId (Integer memberId) {
        var memberList = getProjectMembers();
        for (var member : memberList) {
            if (member.getUserId().equals(memberId) && member.getAccountTypeId() != null) {
                return member.getAccountTypeId();
            }
        }
        return 0;
    }
    public List<ProjectAccountPermissionVo> getAccountPermissionList(Integer memberId, Integer typeId)  {
        validateProjectInitialized(this.projectId);
        var accountPermissionList = LazyHolder.projectAccountPermissionMap.getOrDefault(this.projectId, new ArrayList<>());
        if( accountPermissionList.isEmpty()){
            accountPermissionList = dataService.selectAccountPermissionList(this.projectId);
            synchronized (LazyHolder.lock) {
                if (accountPermissionList != null && !accountPermissionList.isEmpty())
                    LazyHolder.projectAccountPermissionMap.put(this.projectId, accountPermissionList);
            }
        }
        int accountTypeId = getAccountTypeId(memberId);
        if( accountTypeId == 0 || typeId == null || accountPermissionList == null)
            return List.of();
        return accountPermissionList.stream().filter( item -> item.getAccountTypeId().equals(accountTypeId) && item.getTypeId().equals(typeId)).collect(Collectors.toList());
    }
    public List<SysUserSimpleInfoVo> getSysUserList() {
        validateProjectInitialized(stConstant.System_Project_Id);
        return LazyHolder.sysUserMap.getOrDefault(stConstant.System_Project_Id, new ArrayList<>());
    }
    public String getSysUserNickName(Integer userId){
        validateProjectInitialized(stConstant.System_Project_Id);
        var sysUserList = LazyHolder.sysUserMap.getOrDefault(stConstant.System_Project_Id, new ArrayList<>());
        for (var sysUser : sysUserList){
            if( sysUser.getExternalUserId().equals(userId))
                return sysUser.getNickName();
        }
        return "";
    }
    public String getSysUserName(Integer userId){
        validateProjectInitialized(stConstant.System_Project_Id);
        var sysUserList = LazyHolder.sysUserMap.getOrDefault(stConstant.System_Project_Id, new ArrayList<>());
        for (var sysUser : sysUserList){
            if( sysUser.getExternalUserId().equals(userId))
                return sysUser.getUserName();
        }
        return "";
    }
    public String getSysUserEmail(Integer userId){
        if( userId == null)
            return "";
        validateProjectInitialized(stConstant.System_Project_Id);
        var sysUserList = LazyHolder.sysUserMap.getOrDefault(stConstant.System_Project_Id, new ArrayList<>());
        for (var sysUser : sysUserList){
            if( sysUser.getExternalUserId().equals(userId))
                return sysUser.getEmail();
        }
        return "";
    }
    public String getDeptNameById(Long deptId) {
        validateProjectInitialized(stConstant.System_Project_Id);
        String deptName = "";
        var deptNameMp = LazyHolder.sysDeptNameMap.getOrDefault(stConstant.System_Project_Id, new HashMap<>());
        if (deptNameMp.containsKey(deptId))
            deptName =  deptNameMp.get(deptId);
        else{
            //get this dept name from service
            deptName = dataService.getDeptNameById(deptId);
            if( Objects.nonNull(deptName)){
                deptNameMp.put(deptId, deptName);
            }

        }
        return deptName;
    }
    public List<CustomerSimpleInfoVo> getCustomerList() {
        validateProjectInitialized(this.projectId);
        var projectInfo = getProjectInfo();
        if( projectInfo == null)
            return new ArrayList<>();

        Integer baseProjectId = isBaseProject()? this.projectId:projectInfo.getBaseProjectId();
        var customerList = LazyHolder.customerMap.getOrDefault(baseProjectId, new ArrayList<>());
        if( customerList.isEmpty()){
            customerList = dataService.selectCustomerSimpleInfoList(baseProjectId);
            if( customerList != null && !customerList.isEmpty())
                LazyHolder.customerMap.put(baseProjectId, customerList);
        }
        return customerList;
    }
    public String getCustomerName(Integer customerId) {
        var customerList = getCustomerList();
        for (var customer : customerList) {
            if (customer.getCustomerId().equals(customerId)) {
                return customer.getCustomerName();
            }
        }
        return "";
    }
    public void resetCustomerList() {
        synchronized (LazyHolder.lock) {
            if (this.projectId == 0) {
                // 重置所有项目数据
                LazyHolder.customerMap.clear();
            } else {
                // 移除指定项目的相关数据
                //need to check if the project is initialized
                if (!LazyHolder.initializedProjects.contains(this.projectId)) {
                    log.warn( "Project {} was not initialized, nothing to reset", this.projectId );
                    return;
                }

                if( !isBaseProject())
                    return;
                LazyHolder.customerMap.remove(this.projectId);
                var customerList = dataService.selectCustomerSimpleInfoList(this.projectId);
                if( customerList != null && !customerList.isEmpty())
                    LazyHolder.customerMap.put(this.projectId, customerList);
            }
        }
    }
    public List<ProjectPageFieldVo> getProjectPageFields() {
        validateProjectInitialized(this.projectId);
        var pageFieldList =  LazyHolder.projectPageFieldMap.getOrDefault(this.projectId, new ArrayList<>());
        if( pageFieldList.isEmpty() ){
            ProjectPageFieldBo bo = new ProjectPageFieldBo();
            bo.setProjectId(this.projectId);
            pageFieldList = dataService.selectPageFieldList(bo);
            synchronized (LazyHolder.lock) {
                if (!pageFieldList.isEmpty()) {
                    LazyHolder.projectPageFieldMap.put(projectId, pageFieldList);
                }
            }
        }
        return pageFieldList;
    }
    public void ResetProjectPageFields(){
        synchronized (LazyHolder.lock) {
            if (this.projectId == 0) {
                // 重置所有项目数据
                LazyHolder.projectPageFieldMap.clear();
            } else {
                // 移除指定项目的相关数据
                //need to check if the project is initialized
                if (!LazyHolder.initializedProjects.contains(this.projectId)) {
                    log.warn( "Project {} was not initialized, nothing to reset", this.projectId );
                    return;
                }
                LazyHolder.projectPageFieldMap.remove(this.projectId);
            }
        }
    }
    public List<ProjectPageFieldVo> getProjectPageFieldsByIds(List<Integer> fieldIds) {
        var list = getProjectPageFields();
        List<ProjectPageFieldVo> result = new ArrayList<>();
        for (var item : list) {
            if (fieldIds.contains(item.getFieldId())) {
                result.add(item);
            }
        }
        return result;
    }
    public Integer getFieldType(Integer fieldId) {
        var list = getProjectPageFields();
        for (var item : list) {
            if (item.getFieldId().equals(fieldId)) {
                return item.getFieldType();
            }
        }
        return 0;
    }
    public Integer getFieldSubtype(Integer fieldId) {
        var list = getProjectPageFields();
        for (var item : list) {
            if (item.getFieldId().equals(fieldId)) {
                return item.getFieldSubtype() == null ? 0 : item.getFieldSubtype();
            }
        }
        return 0;
    }
    public Boolean isTimestampField(Integer fieldId) {
       return (getFieldType(fieldId) == eFieldTypeDef.PlainText.getValue() || getFieldType(fieldId) == eFieldTypeDef.RichText.getValue()) &&
           getFieldSubtype(fieldId).equals(stConstant.MultipleEdit_Field_SubType_Timestamp);
    }
    public String getFieldName(Integer fieldId) {
        if(FieldIdHelper.IsSystemField( fieldId)){
            return  FieldIdHelper.getSystemFieldName(this.projectId,fieldId);
        }
        var list = getProjectPageFields();
        for (var item : list) {
            if (item.getFieldId().equals(fieldId)) {
                return item.getFieldName();
            }
        }
        return "";
    }
    public void resetProjectFieldSelections(){
        synchronized (LazyHolder.lock) {
            if (this.projectId == 0) {
                // 重置所有项目数据
                LazyHolder.projectFieldSelectionMap.clear();
            } else {
                // 移除指定项目的相关数据
                //need to check if the project is initialized
                if (!LazyHolder.initializedProjects.contains(this.projectId)) {
                    log.warn( "Project {} was not initialized, nothing to reset", this.projectId );
                    return;
                }
                LazyHolder.projectFieldSelectionMap.remove(this.projectId);
                getProjectFieldSelections();//get this selection list again.
            }
        }
    }
    public List<ProjectFieldSelectionVo> getProjectFieldSelections() {
        validateProjectInitialized(this.projectId);
        var selectionList = LazyHolder.projectFieldSelectionMap.getOrDefault(this.projectId, new ArrayList<>());
        if( selectionList.isEmpty() ){
            ProjectFieldSelectionBo bo = new ProjectFieldSelectionBo();
            bo.setProjectId(this.projectId);
            selectionList = dataService.selectFieldSelctionList(bo);
            if (!selectionList.isEmpty()) {
                LazyHolder.projectFieldSelectionMap.put(projectId, selectionList);
            }
        }
        return selectionList;
    }
    public String getChoiceName(Integer fieldId, Integer choiceId) {
        var projectFieldSelections = getProjectFieldSelections();
        for (ProjectFieldSelectionVo selection : projectFieldSelections) {
            if (selection.getFieldId().equals(fieldId) && selection.getChoiceId().equals(choiceId)) {
                return selection.getChoiceName();
            }
        }
        return "";
    }
    public String getFieldChoiceName(Integer fieldId, Integer choiceId, eCultureCode cultureCode) {
        var projectFieldSelections = getProjectFieldSelections();
        for (ProjectFieldSelectionVo selection : projectFieldSelections) {
            if (selection.getFieldId().equals(fieldId) && selection.getChoiceId().equals(choiceId)) {
                return selection.getChoiceName();
            }
        }
        return "";
    }
    public String getFieldChoiceNames(Integer fieldId, List<Integer> choiceIds, eCultureCode cultureCode) {
        StringBuilder sb = new StringBuilder();
        for (Integer choiceId : choiceIds) {
            String choiceName = getFieldChoiceName(fieldId, choiceId, cultureCode);
            if (!choiceName.isEmpty()) {
                sb.append(choiceName).append(",");
            }
        }
        if (!sb.isEmpty()) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
    public Integer getChoiceIdByName(Integer fieldId, String choiceName) {
        var projectFieldSelections = getProjectFieldSelections();
        for (ProjectFieldSelectionVo selection : projectFieldSelections) {
            if (selection.getFieldId().equals(fieldId) && selection.getChoiceName().equals(choiceName)) {
                return selection.getChoiceId();
            }
        }
        return 0;
    }
    public Map<Integer, List<ProjectFieldSelectionVo>> getChoiceIdsByFieldId(FieldChoicesBo field) {
        var list = getProjectFieldSelections();


        Map<Integer, List<ProjectFieldSelectionVo>> map = new HashMap<>();
        var fieldIds = field.getFieldIds();
        if(ObjectUtil.isNull(fieldIds))
            return map;
        boolean isBaseProject = isBaseProject();
        for (Integer fieldId : fieldIds){
            boolean bFound = false;
            if( !isBaseProject) {
                if (fieldId == eSystemFieldDef.Status.getValue()) {
                    var stateList = getWorkflowStates();
                    var stateFieldSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var state : stateList) {
                        var selection = new ProjectFieldSelectionVo();
                        selection.setFieldId(fieldId);
                        selection.setChoiceId(state.getStateId());
                        selection.setChoiceName(state.getStateName());
                        stateFieldSelectionList.add(selection);
                    }
                    map.put(fieldId, stateFieldSelectionList);
                    bFound = true;
                } else if (fieldId == eSystemFieldDef.Owner.getValue()) {
                    var ownerList = getProjectMembers();
                    var ownerFieldSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var owner : ownerList) {
                        var selection = new ProjectFieldSelectionVo();
                        selection.setFieldId(fieldId);
                        selection.setChoiceId(owner.getUserId());
                        selection.setChoiceName(owner.getNickName());
                        ownerFieldSelectionList.add(selection);
                    }
                    map.put(fieldId, ownerFieldSelectionList);
                    bFound = true;
                }
                else if( fieldId == eSystemFieldDef.Type.getValue()){
                    var itemTypeList = getItemTypeList();
                    var itemTypeSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var itemType : itemTypeList){
                        var selection = new ProjectFieldSelectionVo();
                        selection.setFieldId(fieldId);
                        selection.setChoiceId(itemType.getTypeId());
                        selection.setChoiceName(itemType.getTypeName());
                        itemTypeSelectionList.add(selection);
                    }
                    map.put(fieldId, itemTypeSelectionList);
                    bFound = true;
                }
                else if( fieldId == eSystemFieldDef.Customer.getValue()){
                    var customerList = getCustomerList();
                    var customerFieldSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var customer : customerList){
                        var selection = new ProjectFieldSelectionVo();
                        selection.setFieldId(fieldId);
                        selection.setChoiceId(customer.getCustomerId());
                        selection.setChoiceName(customer.getCustomerName());
                        customerFieldSelectionList.add(selection);
                    }
                    map.put(fieldId, customerFieldSelectionList);
                    bFound = true;
                }
                else if( fieldId == eSystemFieldDef.Employee.getValue()){
                    var employeeList = getSysUserList();
                    var employeeFieldSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var employee : employeeList){
                        if( employee.getStUserType() == eSTUserTypeDef.EWPEnabled.getMask() ||
                            employee.getStUserType() == eSTUserTypeDef.SWPEnabled.getMask() ||
                            eSTUserTypeDef.isBothEPAndSPEnabled(employee.getStUserType())) {

                            var selection = new ProjectFieldSelectionVo();
                            selection.setFieldId(fieldId);
                            selection.setChoiceId(employee.getExternalUserId());
                            selection.setChoiceName(employee.getNickName());
                            employeeFieldSelectionList.add(selection);
                        }
                    }
                    map.put(fieldId, employeeFieldSelectionList);
                }
            }
            else  {
                if( fieldId == eUserSystemFieldDef.CreatedBy.getValue()) {
                    var sysUserList = getSysUserList();
                    var ownerFieldSelectionList = new ArrayList<ProjectFieldSelectionVo>();
                    for (var owner : sysUserList) {
                        var selection = new ProjectFieldSelectionVo();
                        selection.setFieldId(fieldId);
                        selection.setChoiceId(owner.getExternalUserId());
                        selection.setChoiceName(owner.getNickName());
                        ownerFieldSelectionList.add(selection);
                    }
                    map.put(fieldId, ownerFieldSelectionList);
                    bFound = true;
                }
            }
            if(!bFound)
            {
                // 在循环内部替换原代码：
                List<ProjectFieldSelectionVo> filteredList = list.stream()
                    .filter(item -> fieldId.equals(item.getFieldId()))
                    .collect(Collectors.toList());

                if (filteredList.isEmpty()) {
                    continue;
                }
                map.put(fieldId, filteredList);
            }
        }
        return map;
    }
    public List<WorkflowStateVo> getWorkflowStates() {
        validateProjectInitialized(this.projectId);
        return LazyHolder.workflowStateMap.getOrDefault(this.projectId, new ArrayList<>());
    }
    public List<WorkflowStateVo> getWorkflowOpenStates(){
        var list = getWorkflowStates();
        List<WorkflowStateVo> result = new ArrayList<>();
        for (var item : list) {
            if ( item.getStateOptionId() != null && item.getStateOptionId() == 0) {
                result.add(item);
            }
        }
        return result;
    }
    public List<WorkflowStateVo> getWorkflowClosedStates(){
        var list = getWorkflowStates();
        List<WorkflowStateVo> result = new ArrayList<>();
        for (var item : list) {
            if (item.getStateOptionId() != null && item.getStateOptionId() == 1) {
                result.add(item);
            }
        }
        return result;
    }
    public boolean isWorkflowStateClosed(Integer stateId){
        var state = getWorkflowState(stateId);
        if( state == null)
            return false;
        return state.getStateOptionId() != null && state.getStateOptionId() == 1;
    }
    private WorkflowStateVo getWorkflowState4FirstState() {
        var stateslist = getWorkflowStates();
        var transactionslist = getWorkflowTransitions();
        for (var item : stateslist) {
            boolean bFound = false;
            for(var transaction : transactionslist){
                // 判断toStateId是否等于当前状态的stateId, 如果等于，则说明不是第一个状态, 则不是第一个状态, 则不是第一个状态
                if( transaction.getToStateId().equals(item.getStateId())) {
                    bFound = true;
                    break;
                }
            }
            if( !bFound)
                return item;
        }
        return null;
    }
    public WorkflowStateVo getWorkflowState(Integer stateId) {
        if( stateId == null)
            return null;
        if( stateId == 0){
           return getWorkflowState4FirstState();
        }
        var list = getWorkflowStates();
        for (var item : list) {
            if (item.getStateId().equals(stateId)) {
                return item;
            }
        }
        return null;
    }
    public String getWorkflowStateName(Integer stateId) {
        var state = getWorkflowState(stateId);
        if (state == null) {
            return "";
        }
        return state.getStateName();
    }
    public List<WorkflowTransitionVo> getWorkflowTransitions() {
        validateProjectInitialized(this.projectId);
        return LazyHolder.workflowTransitionMap.getOrDefault(this.projectId, new ArrayList<>());
    }
    public WorkflowTransitionVo getWorkflowTransition(Integer transitionId) {
        var list = getWorkflowTransitions();
        for (var item : list) {
            if (item.getTransitionId().equals(transitionId)) {
                return item;
            }
        }
        return null;
    }
    public String getWorkflowTransitionName(Integer transitionId){
        var transition = getWorkflowTransition(transitionId);
        String transactionName =  "";
        if( transition != null)
             transactionName = transition.getTransitionName();
        return  transactionName;
    }
    public WorkflowTransitionStateVo getWorkflowTransitionNextState(Integer stateId) {
        var state = getWorkflowState(stateId);
        if (state == null) {
            return null;
        }
        var list = getWorkflowTransitions();
        var result = new WorkflowTransitionStateVo();
        stateId = state.getStateId();
        result.setFromStateId(state.getStateId());
        result.setFromStateName(state.getStateName());
        result.setNextStates(new ArrayList<>());
        for (var item : list) {
            if (item.getFromStateId().equals(stateId)) {
                var nextState = getWorkflowState(item.getToStateId());
                if (nextState == null) {
                    log.warn("Missing state for toStateId: {}", item.getToStateId());
                    continue;
                }
                result.getNextStates().add(new WorkflowTransitionNextStateVo(
                    item.getTransitionId(),
                    item.getTransitionName(),
                    item.getToStateId(),
                    nextState.getStateName(),
                    item.getDisplayOrder(),
                    nextState.getStateOptionId())
                );
            }
        }
        if(!result.getNextStates().isEmpty())
          result.getNextStates().sort(Comparator.comparingInt(s -> s.getDisplayOrder() == null ? 0 : s.getDisplayOrder()));
        return result;
    }
    public  List<ProjectItemTypeVo> getItemTypeList() {
        validateProjectInitialized(this.projectId);
        return LazyHolder.projectItemTypeMap.getOrDefault(this.projectId, new ArrayList<>());
    }
    public  String getItemTypeName(Integer itemTypeId) {
        var list  = getItemTypeList();
        for (var item : list) {
            if (item.getTypeId().equals(itemTypeId)) {
                return item.getTypeName();
            }
        }
        return "";
    }
    public  List<NotificationRuleListItemVo> getNotificationRule(Integer objectId, List<Integer> ruleTypeList) {
        validateProjectInitialized(this.projectId);
        var list = LazyHolder.projectNotificationRuleMap.getOrDefault(this.projectId, new ArrayList<>());
        return list.stream().filter(r -> r.getObjectId().equals(objectId) && ruleTypeList.contains(r.getRuleType())).toList();
    }
    public boolean isEmailEnabled(){
        validateProjectInitialized(this.projectId);
        var projectInfo = LazyHolder.projectInfoMap.get(this.projectId);
        if( projectInfo == null)
            return false;
        return projectInfo.getEnableEmail();
    }

    private void validateProjectInitialized(Integer projectId) {
        if (!LazyHolder.initializedProjects.contains(projectId)) {
            //throw new IllegalStateException("Project " + projectId + " is not initialized");
            log.warn("Project {} is not initialized, or reset to rebuild data", projectId);
            ensureProjectInitialized(projectId);
        }
    }
}
