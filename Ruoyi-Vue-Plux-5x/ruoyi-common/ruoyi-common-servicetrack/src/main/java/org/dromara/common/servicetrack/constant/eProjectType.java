package org.dromara.common.servicetrack.constant;

public enum eProjectType  implements IValueEnum {
    NONE(0),
    INCIDENT(1),
    KNOWLEDGE(2),
    BASE_PROJECT(10);

    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eProjectType(int value) {
        this.value = value;
    }
    @Override
    public int getValue() {
        return value;
    }
    public static eProjectType from(Integer value) {
        return IValueEnum.valueOf(eProjectType.class, value);
    }
}
