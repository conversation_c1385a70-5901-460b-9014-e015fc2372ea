package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectGroupUserBo;
import org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目分组用户 服务层
 *
 * <AUTHOR>
 */
public interface IProjectGroupUserService {

    /**
     * 查询项目分组用户
     */
    List<ProjectGroupUserVo> selectGroupUserList(Integer projectId, Integer groupId);
    /**
     * 查询项目分组用户
     */
    ProjectGroupUserVo queryById(Long id);

    /**
     * 查询项目分组用户列表
     */
    TableDataInfo<ProjectGroupUserVo> queryPageList(Integer projectId, PageQuery pageQuery);

    /**
     * 新增项目分组用户
     */
    Boolean insertByBo(ProjectGroupUserBo bo);

    /**
     * 修改项目分组用户
     */
    Boolean updateByBo(ProjectGroupUserBo bo);
    /**
     * 校验并批量删除项目分组用户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
