package org.dromara.common.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldCustomBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldCustomVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目自定义字段 服务层
 *
 * <AUTHOR>
 */
public interface IProjectFieldCustomService {
    /**
     * 查询项目自定义字段
     */
    ProjectFieldCustomVo queryById(Long keyId);
    /**
     * 查询项目自定义字段列表
     */
    TableDataInfo<ProjectFieldCustomVo> queryPageList(Integer projectId, PageQuery pageQuery);
    /**
     * 查询项目自定义字段列表
     */
    List<ProjectFieldCustomVo> queryList(Integer projectId);
    /**
     * 新建项目自定义字段
     */
    Integer insertByBo(ProjectFieldCustomBo bo);

    /**
     * 更新项目自定义字段
     */
    Integer updateByBo(ProjectFieldCustomBo bo);

    /**
     * 删除项目自定义字段
     *
     */
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);
}
