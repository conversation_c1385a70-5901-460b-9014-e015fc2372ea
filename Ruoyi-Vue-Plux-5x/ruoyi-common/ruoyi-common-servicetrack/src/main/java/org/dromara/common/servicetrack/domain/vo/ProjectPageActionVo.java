package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectPageAction;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = ProjectPageAction.class)
public class ProjectPageActionVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * project id
     */
    private Integer projectId;
    /**
     * item type id
     */
    private Integer itemTypeId;
    /**
     * page id
     */
    private Integer pageId;
    /**
     * action id
     */
    private Integer actionId;
    /**
     * page order
     */
    private Integer pageOrder;

    /**
     * type id the same as item type id
     */
    private Integer typeId;
    /**
     * item type name
     */
    private String typeName;
    /**
     * transition id
     */
    private Integer transitionId;
}
