package org.dromara.common.servicetrack.domain.vo;

import java.io.Serializable;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 字段对象VO
 *
 * <AUTHOR> fei
 */

@Data
@NoArgsConstructor
public class ProjectFieldVo implements Serializable{

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * field id
     */
    private Integer fieldId;

    /**
     * field name
     */
    private String fieldName;

    /**
     * field type
     */
    private Integer fieldType;

    /**
     * 字段子类型
     */
    private Integer fieldSubtype;

    /**
     * module id
     */
    private Integer moduleId;

    /**
     * formula
     */
    private String formula;

}
