package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 项目页面类型排序绑定对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPageActionSortBo extends STBaseEntity {
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "条目类型ID不能为空")
    private Integer itemTypeId;
    @NotNull(message = "操作ID不能为空")
    private Integer actionId;

    /*
     * 排序页面Ids
     */
    private List<Integer> pageIds;
}
