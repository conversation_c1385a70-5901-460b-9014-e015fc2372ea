package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectFieldPcSelection;

/**
 * 项目字段父子选择项关系业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectFieldPcSelection.class)
public class ProjectFieldPcSelectionBo extends STBaseEntity {
    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 父字段ID
     */
    @NotNull(message = "父字段ID不能为空")
    private Integer parentFieldId;

    /**
     * 子字段ID
     */
    @NotNull(message = "子字段ID不能为空")
    private Integer childFieldId;

    /**
     * 父选择项ID
     */
    @NotNull(message = "父选择项ID不能为空")
    private Integer parentChoiceId;

    /**
     * 子选择项ID
     */
    @NotNull(message = "子选择项ID不能为空")
    private Integer childChoiceId;
}
