package org.dromara.common.servicetrack.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;


import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.servicetrack.constant.IBaseFieldEnum;
import org.dromara.common.servicetrack.constant.IValueEnum;
import org.dromara.common.servicetrack.constant.eSTModuleIDDef;
import org.dromara.common.servicetrack.domain.ProjectFieldCalculation;
import org.dromara.common.servicetrack.domain.ProjectFieldCustom;
import org.dromara.common.servicetrack.domain.ProjectSystemField;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldCalculationVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectFieldCalculationMapper;
import org.dromara.common.servicetrack.mapper.ProjectSystemFieldMapper;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 字段管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@RequiredArgsConstructor
@Service
public class ProjectSystemFieldServiceImpl implements IProjectSystemFieldService {
    private final ProjectSystemFieldMapper projectSystemFieldMapper;
    private final ProjectFieldCalculationMapper projectFieldCalculationMapper;

    /**
     * 根据条件查询所有字段列表
     *
     * @param field 字段信息
     * @return 字段信息集合信息
     */
    @Override
    public List<ProjectFieldVo> selectFieldList(ProjectSystemFieldBo field) {
        if(field.getModuleId() == null ||  field.getModuleId() == 0){
             throw new ServiceException("Module Id can't be 0");
        }
        QueryWrapper<ProjectSystemField> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq(ObjectUtil.isNotNull(field.getFieldId()), "field_id", field.getFieldId())
            .eq(ObjectUtil.isNotNull(field.getProjectId()), "project_id",field.getProjectId())
            .eq(ObjectUtil.isNotNull(field.getModuleId()), "module_id", field.getModuleId());


        var fieldList = projectSystemFieldMapper.selectFieldList(queryWrapper);
        //check if missing some system fields, if so, add them
        Integer projectId = field.getProjectId();
        var calcFieldList = projectFieldCalculationMapper.selectVoList(new LambdaQueryWrapper<ProjectFieldCalculation>()
            .eq(ProjectFieldCalculation::getProjectId, field.getProjectId()));
        if( !calcFieldList.isEmpty()){
            for (ProjectFieldCalculationVo calcField : calcFieldList) {
                fieldList.stream().filter(fieldVo -> fieldVo.getFieldId().equals(calcField.getFieldId())).findFirst()
                    .ifPresent(matchedField -> matchedField.setFormula(calcField.getFormula()));
            }
        }
        List<ProjectSystemField> missingSystemFields = new ArrayList<>();
        IValueEnum[] systemFields = null;
        int moduleId = field.getModuleId();
        if (moduleId == eSTModuleIDDef.CustomerInfo.getValue()) {
            systemFields = FieldIdHelper.AllCustomerInfoSystemFields;
        }else if (moduleId == eSTModuleIDDef.ContactInfo.getValue()) {
            systemFields = FieldIdHelper.AllContactSystemFields;
        } else if (moduleId == eSTModuleIDDef.UserInfo.getValue()) {
            systemFields = FieldIdHelper.AllUserInfoSystemFields;
        } else if (moduleId == eSTModuleIDDef.Incident.getValue()) {
            systemFields = FieldIdHelper.AllModuleSystemFields;
        }
        if( systemFields == null)
            return fieldList;
        for (var systemFieldId : systemFields) {
            int fieldId = systemFieldId.getValue();
            if( fieldId == 0)
                continue;
            boolean found = false;
            for (int j = 0; j < fieldList.size(); j++) {
                ProjectFieldVo field1 = fieldList.get(j);
                if (field1.getFieldId() == fieldId) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                ProjectSystemFieldBo bo1 = new ProjectSystemFieldBo();
                bo1.setProjectId(projectId);
                bo1.setFieldId(fieldId);
                bo1.setFieldType(((IBaseFieldEnum)systemFieldId).getFieldType());
                bo1.setFieldName(((IBaseFieldEnum)systemFieldId).getName());
                bo1.setFieldDefaultName(((IBaseFieldEnum)systemFieldId).getDefaultName());
                bo1.setModuleId(field.getModuleId());
                ProjectSystemField sysField = MapstructUtils.convert(bo1, ProjectSystemField.class);
                missingSystemFields.add(sysField);
            }
        }
        if (!missingSystemFields.isEmpty()) {
            projectSystemFieldMapper.insertBatch(missingSystemFields);

            fieldList = projectSystemFieldMapper.selectFieldList(queryWrapper);
        }
        return fieldList;
    }

    @Override
    public Integer updateByBo(ProjectSystemFieldBo bo) {
        ProjectSystemField update = MapstructUtils.convert(bo, ProjectSystemField.class);
        return projectSystemFieldMapper.updateById(update) > 0 ? bo.getFieldId() : 0;
    }
}
