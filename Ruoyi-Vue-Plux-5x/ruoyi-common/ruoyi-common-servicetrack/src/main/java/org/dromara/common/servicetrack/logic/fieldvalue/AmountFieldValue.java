package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.utils.ValueConvert;

import java.util.Objects;

public class AmountFieldValue extends BaseFieldValue{
    private Double rawValue;
    @Override
    public Boolean equalsTo(IFieldValue other) {
        return equals2((AmountFieldValue) other);
    }
    private Boolean equals2(AmountFieldValue other) {
        if (other == null) {
            return false;
        } else {
            return Objects.equals(rawValue, other.rawValue);
        }
    }
    @Override
    public String getDisplayValue() {
        double dValue = 0.0;
        if (rawValue != null) {
            dValue = rawValue;
        }

        return String.format("%.2f", dValue);
    }

    @Override
    public Object getRawValue() {
        return rawValue;
    }

    @Override
    public void readValueFromDB(Object data) {
        if (ValueConvert.isNull(data)) {
            rawValue = null;
        } else {
            rawValue = ValueConvert.readDouble(data);
        }
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if (value == null) {
            rawValue = null;
        } else {
            String str = value.toString();
            if (str.isEmpty()) {
                rawValue = null;
            } else {
                if (str.matches("^-?\\d+(\\.\\d{1,2})?$")) {
                    rawValue = ValueConvert.readDouble(str);
                } else {
                    rawValue = null;
                }
            }
        }
    }

    @Override
    public String toCustomFieldFormatString() {
        return getDisplayValue();
    }

    @Override
    public Boolean isUnassigned() {
        return rawValue ==  null || rawValue == 0.0;
    }
}
