package org.dromara.common.servicetrack.sequence;

/**
 * 需要序列号的表枚举
 *
 * <AUTHOR>
 */
public enum SequenceTable {

    /**
     * 用户表(sys_user)
     */
    USER,
    /**
     * 项目表(project_info)
     */
     PROJECT,

    /**
     * 条目表(item_info)
     */
    Item_Info,

    /**
     * 条目changelog(item_changelog)
     */
    Item_Changelog,

    /**
     * 条目history(item_history)
     */
    Item_History,

    /**
     * 条目附件(item_attachment)
     */
    Item_Attachment,

    /**
     * 项目账户(project_accountType)
     */
    Project_AccountType,

    /**
     * 项目分组(project_group)
     */
    Project_Group,

    /**
     * 项目自定义字段(project_field_custom)
     */
    Project_Field_Custom,

    /**
     * 项目Item 类型(project_item_type)
     */
    Project_Item_Type,
    /**
     * 项目自定义页面(project_page)
     */
    Project_Page,

    /**
     * User info changelog(user_info_changelog)
     */
    UserInfo_Changelog,

    /*
    * 通知规则(notification_rule)
     */
    Notification_Rule,

    /*
    * 通知规则Email模板(notification_email_template)
     */
    Notification_Email_Template,

    /*
    * 邮件队列(notification_email_queue)
     */
    Notification_Email_Queue,

    /**
     * 邮件日志(notification_email_log)
     */
    Notification_Email_Log,

    /**
     * 客户信息(customer_info)
     */
    Customer_Info,

    /**
     * 客户信息变更日志(customer_info_changelog)
     */
    Customer_Info_Changelog,

    /**
     * 联系人信息变更日志(contact_info_changelog)
     */
    Contact_Info_Changelog,

    /**
     * 工作流信息(workflow_info)
     */
    Workflow_Info,
    /**
     * 工作流状态(workflow_state)
     */
    Workflow_State,
    /**
     * 工作流转换(workflow_transition)
     */
    Workflow_Transition,

    /**
     * 文件夹信息(folder_info)
     */
    Folder_Info,
    /**
     * 报告信息(report_info)
     */
    Report_Info
}
