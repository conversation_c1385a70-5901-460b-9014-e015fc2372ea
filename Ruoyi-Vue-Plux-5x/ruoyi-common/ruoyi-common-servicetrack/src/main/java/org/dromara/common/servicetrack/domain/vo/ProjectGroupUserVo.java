package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectGroupUser;

import java.io.Serial;
import java.io.Serializable;

/**
 * 项目分组用户视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProjectGroupUser.class)
public class ProjectGroupUserVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 分组ID
     */
    private Integer groupId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;
}
