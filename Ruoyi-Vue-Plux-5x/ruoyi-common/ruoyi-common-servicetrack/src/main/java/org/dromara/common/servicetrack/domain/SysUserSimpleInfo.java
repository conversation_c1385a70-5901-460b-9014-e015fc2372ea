package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * user对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUserSimpleInfo extends STBaseEntity {
    /** ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * user id
     */
    @TableField(value = "external_user_id")
    private Integer externalUserId;

    /**
     * st user type
     **/
    @TableField(value = "st_user_type")
    private Integer stUserType;

    /**
     * user name
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * nick name
     */
    @TableField(value = "nick_name")
    private String nickName;

}
