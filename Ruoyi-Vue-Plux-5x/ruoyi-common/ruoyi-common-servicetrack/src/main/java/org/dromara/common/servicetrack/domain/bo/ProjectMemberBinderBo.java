package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectMemberBinderBo extends STBaseEntity {
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * 成员
     */
    List<ProjectMemberInfo> projectMembers;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectMemberInfo extends STBaseEntity {
        /**
         * user id
         */
        @NotNull(message = "UserID不能为空")
        private Integer userId;

        /**
         * user type
         **/
        private Integer userType;

        /**
         * account type id
         **/
        private Integer accountTypeId;
    }
}
