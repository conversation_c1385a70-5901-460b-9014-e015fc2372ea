package org.dromara.common.servicetrack.domain.vo;

import java.io.Serial;
import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectFieldCalculation;

/**
 * project_field_calculation表的vo类
 */
@Data
@AutoMapper(target = ProjectFieldCalculation.class)
public class ProjectFieldCalculationVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * key_id
     */
    private Long id;

    /**
     * project_id
     */
    private Integer projectId;

    /**
     * field_id
     */
    private Integer fieldId;

    /**
     * formula
     */
    private String formula;
}
