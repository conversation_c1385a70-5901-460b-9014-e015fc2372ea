package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.utils.ValueConvert;

public class RichTextFieldValue extends PlainTextFieldValue{
    @Override
    public Boolean equalsTo(IFieldValue other) {
        return super.equalsTo(other);
    }

    @Override
    public String getDisplayValue() {
        return super.getDisplayValue();
    }

    @Override
    public Object getRawValue() {
        return super.getRawValue();
    }

    @Override
    public void readValueFromDB(Object data) {
        super.readValueFromDB(data);
    }

    @Override
    public void setFieldValue(Object value, Integer option) {
        if (value == null) {
            rawValue = null;
        } else if (value instanceof String) {
            //base64 decode
           value = ValueConvert.decodeBase64(value);
        }
        super.setFieldValue(value, option);
    }

    @Override
    public String toCustomFieldFormatString() {
        return super.toCustomFieldFormatString();
    }

    @Override
    public Boolean isUnassigned() {
        return super.isUnassigned();
    }
}
