package org.dromara.common.servicetrack.constant;

public enum eUserSetting implements IValueEnum {
    NONE(0),
    ListView_SelectedColumns(1),
    Login_Select_Default_ST_UserType(2),
    ListView_SelectedColumns_Customer(3),
    ListView_SelectedColumns_Contact(4),
    Last_Setting(1000);

    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eUserSetting(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static eUserSetting from(Integer value) {
        return IValueEnum.valueOf(eUserSetting.class, value);
    }
}
