package org.dromara.common.servicetrack.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ItemAttachment;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Item 附件视图对象 item_attachment
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ItemAttachment.class)
public class ItemAttachmentVo implements Serializable{
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    private Integer projectId;

    /**
     * item id
     */
    private Integer itemId;
    /**
     * 附件Id
     */
    private Integer attachmentId;

    /**
     * 关联的OSS ID
     */
    private Long ossId;
    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 文件相对路径
     */
    @JsonIgnore
    private String fileName;

    /**
     * 附件url
     */
    private String url;

    /**
     * 附件 thumbnail url
     */
    private String thumbnailUrl;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 上传人
     */
    private Integer createdBy;

    /**
     * 上传人名称
     */
    private String createdByName;

}
