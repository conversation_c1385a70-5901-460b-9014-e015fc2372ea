package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.servicetrack.domain.WorkflowStateOwners;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateOwnersBo;
import org.dromara.common.servicetrack.domain.vo.WorkflowStateOwnersVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 工作流状态负责人 数据层
 *
 * <AUTHOR> fei
 */
public interface WorkflowStateOwnersMapper extends BaseMapperPlus<WorkflowStateOwners, WorkflowStateOwnersVo> {
    default LambdaQueryWrapper<WorkflowStateOwners> buildWrapper(WorkflowStateOwnersBo bo) {
        LambdaQueryWrapper<WorkflowStateOwners> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, WorkflowStateOwners::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, WorkflowStateOwners::getProjectId, bo.getProjectId());
        lqw.eq(bo.getStateId() != null, WorkflowStateOwners::getStateId, bo.getStateId());
        lqw.eq(bo.getUserId() != null, WorkflowStateOwners::getUserId, bo.getUserId());
        lqw.eq(bo.getUserType() != null, WorkflowStateOwners::getUserType, bo.getUserType());

        return lqw;
    }
}
