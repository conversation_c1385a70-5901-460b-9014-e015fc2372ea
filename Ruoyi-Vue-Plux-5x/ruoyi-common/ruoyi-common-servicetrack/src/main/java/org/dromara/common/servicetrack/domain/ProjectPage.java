package org.dromara.common.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_page")
public class ProjectPage extends STBaseEntity {
    @TableId(value = "key_id")
    private Long id;

    @TableField(value = "project_id")
    private Integer projectId;

    @TableField(value = "page_id")
    private Integer pageId;

    @TableField(value = "page_name")
    private String pageName;

    @TableField(value = "module_id")
    private Integer moduleId;
}
