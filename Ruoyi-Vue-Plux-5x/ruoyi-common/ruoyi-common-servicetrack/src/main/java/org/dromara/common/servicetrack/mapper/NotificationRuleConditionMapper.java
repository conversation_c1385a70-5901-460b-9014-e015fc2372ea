package org.dromara.common.servicetrack.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.NotificationRuleCondition;
import org.dromara.common.servicetrack.domain.bo.NotificationRuleConditionBo;
import org.dromara.common.servicetrack.domain.vo.NotificationRuleConditionVo;

/**
 * notification_rule_condition表的mapper接口
 *
 * <AUTHOR> fei
 */
public interface NotificationRuleConditionMapper extends BaseMapperPlus<NotificationRuleCondition, NotificationRuleConditionVo> {
    /**
     * 查询notification_rule_condition表的所有数据
     *
     * @return 返回notification_rule_condition表的所有数据
     */
    List<NotificationRuleConditionVo> selectAll();

    /**
     * 根据主键查询notification_rule_condition表的数据
     *
     * @param projectId  主键值
     * @param ruleId     主键值
     * @param conditionId 主键值
     * @return 返回主键对应的数据
     */
    NotificationRuleConditionVo selectByPk(@Param("projectId") Integer projectId, @Param("ruleId") Integer ruleId, @Param("conditionId") Integer conditionId);

}
