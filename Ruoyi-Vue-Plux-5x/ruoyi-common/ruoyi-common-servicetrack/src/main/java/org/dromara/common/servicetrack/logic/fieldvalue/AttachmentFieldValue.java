package org.dromara.common.servicetrack.logic.fieldvalue;

import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.servicetrack.utils.ValueConvert;

public class AttachmentFieldValue extends BaseFieldValue{

    private final Integer projectId;
    private final Integer fieldId;
    private String attachmentValue;
    public AttachmentFieldValue(Integer projectId, Integer fieldId)
    {
        this.projectId = projectId;
        this.fieldId = fieldId;
    }
    @Override
    public Boolean equalsTo(IFieldValue other) {
        return null;
    }

    @Override
    public String getDisplayValue() {
        return "";
    }

    @Override
    public Object getRawValue() {
        return null;
    }

    @Override
    public void readValueFromDB(Object data) {

    }

    @Override
    public void setFieldValue(Object value, Integer option) {

    }

    @Override
    public String toCustomFieldFormatString() {
        return "";
    }

    @Override
    public Boolean isUnassigned() {
        return null;
    }
}
