package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 工作流操作权限绑定Bo
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowTransitionPermissionBinderBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * transition_id
     */
    @NotNull(message = "转换ID不能为空")
    @Min(value = 1, message = "transitionId 必须大于 0")
    private Integer transitionId;

    private List<WorkflowTransitionPermissionInfo> permissions;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class WorkflowTransitionPermissionInfo extends STBaseEntity {

        private Long id;
        /**
         * user_id
         */
        @NotNull(message = "用户ID不能为空")
        private Integer userId;

        /**
         * user_type
         */
        @NotNull(message = "用户类型不能为空")
        private Integer userType;
    }
}
