package org.dromara.common.servicetrack.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.WorkflowTransition;

import java.io.Serializable;

/**
 * 工作流转换VO
 *
 * <AUTHOR> fei
 */

@Data
@AutoMapper(target = WorkflowTransition.class)
public class WorkflowTransitionVo implements Serializable{
    /**
     * ID
     */
    private Long id;

    /**
     * ProjectID
     */
    private Integer projectId;

    /**
     * Transition_id
     */
    private Integer transitionId;

    /**
     * Transition_name
     */
    private String transitionName;

   /**
     * From_state_id
     */
    private Integer fromStateId;
    /**
     * From_state_identifier
     */
    private String fromStateIdentifier;

    /**
     * To_state_id
     */
    private Integer toStateId;
    /**
     * To_state_identifier
     */
    private String toStateIdentifier;

    /**
     * Display_order
     */
    private Integer displayOrder;
    /**
     * transition_identifier
     */
    private String transitionIdentifier;

    /**
     * transition_description
     */
    private String transitionDescription;
}
