package org.dromara.common.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.ProjectItemType;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = ProjectItemType.class)
public class ProjectItemTypeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * project id
     */
    private Integer projectId;
    /**
     * item type id
     */
    private Integer typeId;
    /**
     * item type name
     */
    private String typeName;
    /**
     * transaction id
     */
    private Integer transactionId;
}
