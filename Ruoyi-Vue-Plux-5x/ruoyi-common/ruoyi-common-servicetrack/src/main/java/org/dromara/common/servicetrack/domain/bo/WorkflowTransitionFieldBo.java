package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.WorkflowTransitionField;

/**
 * 工作流转换字段Bo
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorkflowTransitionField.class)
public class WorkflowTransitionFieldBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * transition_id
     */
    @NotNull(message = "转换ID不能为空")
    @Min(value = 1, message = "transitionId 必须大于 0")
    private Integer transitionId;

    /**
     * field_id
     */
    @NotNull(message = "字段ID不能为空")
    @Min(value = 1, message = "fieldId 必须大于 0")
    private Integer fieldId;

    /**
     * option_id
     */
    private Integer optionId;
}
