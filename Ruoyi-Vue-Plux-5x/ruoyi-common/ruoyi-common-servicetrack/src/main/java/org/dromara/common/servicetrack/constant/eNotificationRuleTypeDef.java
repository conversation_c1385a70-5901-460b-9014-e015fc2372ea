package org.dromara.common.servicetrack.constant;

public enum eNotificationRuleTypeDef implements IValueEnum{
    NewItem(1),
    OwnerChanged(2),
    stateChanged(3);
    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eNotificationRuleTypeDef(int value) {
        this.value = value;
    }
    @Override
    public int getValue() {
        return value;
    }
    public static eNotificationRuleTypeDef from(Integer value) {
        return IValueEnum.valueOf(eNotificationRuleTypeDef.class, value);
    }
}
