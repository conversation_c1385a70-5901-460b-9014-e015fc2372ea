<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationEmailTemplateMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.NotificationEmailTemplateVo" id="NotificationEmailTemplateResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="templateId" column="template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="emailSubject" column="email_subject"/>
        <result property="emailBody" column="email_body"/>
    </resultMap>

    <sql id="selectNotificationEmailTemplateVo">
        select *
        from notification_email_template
    </sql>

    <select id="selectByProjectAndTemplate" resultMap="NotificationEmailTemplateResult">
        <include refid="selectNotificationEmailTemplateVo"/>
        where project_id = #{projectId} and template_id = #{templateId}
    </select>

    <select id="selectByProjectAndRule" resultMap="NotificationEmailTemplateResult">
        select et.key_id, et.template_id,et.template_name, et.email_subject, et.email_body
        from notification_email_template et
        left join notification_rule_template rt on et.project_id = rt.project_id and et.template_id = rt.template_id
        where et.project_id = #{projectId} and rt.rule_id = #{ruleId}
    </select>

    <select id="selectByProjectId" resultMap="NotificationEmailTemplateResult">
        select key_id, template_id, template_name
        from notification_email_template
        where project_id = #{projectId}
        <if test="templateId != null and templateId > 0" >
            and template_id = #{templateId}
        </if>
        order by template_id
    </select>

</mapper>
