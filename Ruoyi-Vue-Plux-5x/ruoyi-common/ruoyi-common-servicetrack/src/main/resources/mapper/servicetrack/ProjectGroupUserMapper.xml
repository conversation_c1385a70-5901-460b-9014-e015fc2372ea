<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectGroupUserMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="ProjectGroupUserResultMap" type="org.dromara.common.servicetrack.domain.vo.ProjectGroupUserVo">
        <id column="key_id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="group_id" property="groupId" />
        <result column="user_id" property="userId" />
        <result column="nick_name" property="userName" />
    </resultMap>

    <select id="selectGroupUserList" parameterType="Integer" resultMap="ProjectGroupUserResultMap">
        select u.nick_name, u.user_name, g.key_id, g.group_id,  g.user_id
        from project_group_user g
        left join sys_user u on u.external_user_id = g.user_id
        where g.project_id = #{projectId} and g.group_id = #{groupId}
    </select>

    <delete id="deleteGroupUserByGroupIds">
        DELETE FROM project_group_user
        WHERE project_id = #{projectId}
        AND group_id IN
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>
</mapper>
