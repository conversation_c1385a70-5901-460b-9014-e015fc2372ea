<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectFieldSelectionMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo" id="FieldSelectionInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="choiceId" column="choice_id"/>
        <result property="choiceName" column="choice_name"/>
        <result property="choiceOrder" column="choice_order"/>
        <result property="choiceCount" column="choice_count"/>
    </resultMap>
    <select id="selectFieldSelectionByItemSelection" resultMap="FieldSelectionInfoResult">
        SELECT COUNT(1) as choice_count
        FROM item_selection
        WHERE project_id = #{projectId}
        AND field_id IN
        <foreach item="fieldId" collection="fieldIds" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
        AND choice_id IN
        <foreach item="choiceId" collection="choiceIds" open="(" separator="," close=")">
            #{choiceId}
        </foreach>
    </select>
</mapper>
