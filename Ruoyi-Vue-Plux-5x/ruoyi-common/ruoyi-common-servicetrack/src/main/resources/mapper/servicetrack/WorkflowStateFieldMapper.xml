<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.WorkflowStateFieldMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.WorkflowStateFieldVo" id="WorkflowStateFieldResult">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="stateId" column="state_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="optionId" column="option_id"/>
    </resultMap>
</mapper>
