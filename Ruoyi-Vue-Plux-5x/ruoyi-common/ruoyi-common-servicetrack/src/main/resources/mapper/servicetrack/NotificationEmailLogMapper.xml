<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationEmailLogMapper">
    <!-- 查询所有 -->
    <select id="selectAll" resultType="org.dromara.common.servicetrack.domain.vo.NotificationEmailLogVo">
        SELECT * FROM notification_email_log
    </select>

    <!-- 根据主键查询 -->
    <select id="selectByPk" parameterType="map" resultType="org.dromara.common.servicetrack.domain.vo.NotificationEmailLogVo">
        SELECT * FROM notification_email_log WHERE log_id = #{logId}
    </select>


    <!-- 删除数据 -->
    <delete id="deleteByLogIds">
        DELETE FROM notification_email_log
        WHERE log_id IN
        <foreach collection="listLogIds" item="logId" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

</mapper>
