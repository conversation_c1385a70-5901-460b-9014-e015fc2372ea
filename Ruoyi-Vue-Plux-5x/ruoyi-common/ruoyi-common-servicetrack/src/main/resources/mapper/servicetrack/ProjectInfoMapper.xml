<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectInfoMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectInfoVo" id="ProjectInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="projectKey" column="project_key"/>
        <result property="isActive" column="isactive"/>
        <result property="baseProjectId" column="baseproject_id"/>
        <result property="projectDescription" column="project_description"/>
    </resultMap>
    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectInfoOpenAndClosedCountVo" id="OpenAndClosedCountMap">
    </resultMap>
    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectSettingInCacheVo" id="ProjectSettingResult">
        <id property="id" column="key_id"/>
    </resultMap>
    <select id="getEmailSetting"  resultType="java.lang.String">
        select setting_content
        from project_setting
        where project_id = #{projectId} and setting_id=#{settingId}
    </select>
    <select id="getOpenAndClosedCount" resultMap="OpenAndClosedCountMap">
        SELECT
        project_id,
        state_id,
        COUNT(*) AS count
        FROM
        item_info
        WHERE
        project_id IN
        <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        AND (del_flag = '0' or del_flag is null)  -- 假设只统计未删除的记录
        GROUP BY
        project_id, state_id
        ORDER BY
        project_id, state_id;
    </select>
    <select id="getOpenIncidentCountByOwner" resultType="Integer">
        SELECT
        COUNT(*) AS count
        FROM
        item_info
        WHERE project_id = #{projectId} AND owner_id = #{ownerId} AND
        state_id IN
        <foreach item="stateId" collection="stateIds" open="(" separator="," close=")">
            #{stateId}
        </foreach>
        AND (del_flag = '0' or del_flag is null)  -- 假设只统计未删除的记录
        GROUP BY
        project_id, owner_id
        ORDER BY
        project_id, owner_id;
    </select>
    <select id = "getProjectSettingList" resultMap="ProjectSettingResult">
        select key_id, project_id, setting_id, setting_name, setting_option, setting_content
        from project_setting
        where project_id = #{projectId}
        <if test="settingIds != null and settingIds.size() > 0">
            and setting_id in
            <foreach collection="settingIds" item="settingId" open="(" separator="," close=")">
                #{settingId}
            </foreach>
        </if>
    </select>
</mapper>
