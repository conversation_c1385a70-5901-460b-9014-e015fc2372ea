<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectFieldPcMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectFieldPcVo" id="ProjectFieldPcResult">
        <id column="key_id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="parent_field_id" property="parentFieldId" />
        <result column="child_field_id" property="childFieldId" />
    </resultMap>

    <sql id="selectProjectFieldPcVo">
        select key_id, project_id, parent_field_id, child_field_id from project_field_pc
    </sql>

    <select id="selectFieldPcList" parameterType="Integer" resultMap="ProjectFieldPcResult">
        <include refid="selectProjectFieldPcVo"/>
        where project_id = #{projectId}
        <if test="parentFieldId != null">
            and parent_field_id = #{parentFieldId}
        </if>
        order by parent_field_id, child_field_id
    </select>

    <select id="selectChildFieldList" parameterType="Integer" resultMap="ProjectFieldPcResult">
        <include refid="selectProjectFieldPcVo"/>
        where project_id = #{projectId}
        and parent_field_id = #{parentFieldId}
        order by child_field_id
    </select>

    <select id="selectParentFieldList" parameterType="Integer" resultMap="ProjectFieldPcResult">
        <include refid="selectProjectFieldPcVo"/>
        where project_id = #{projectId}
        and child_field_id = #{childFieldId}
        order by parent_field_id
    </select>

    <delete id="deleteFieldPcByFieldIds">
        DELETE FROM project_field_pc
        WHERE project_id = #{projectId}
        AND (parent_field_id IN
        <foreach collection="fieldIds" item="fieldId" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
        OR child_field_id IN
        <foreach collection="fieldIds" item="fieldId" open="(" separator="," close=")">
            #{fieldId}
        </foreach>)
    </delete>
</mapper>
