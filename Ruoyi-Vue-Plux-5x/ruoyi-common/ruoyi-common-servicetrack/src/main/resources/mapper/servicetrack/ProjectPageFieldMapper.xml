<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectPageFieldMapper">
    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo" id="ProjectPageFieldResult">
        <id property="id" column="key_id"/>
        <result property="pageId" column="page_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="pageRow" column="page_row"/>
        <result property="pageColumn" column="page_column"/>
        <result property="pageName" column="page_name"/>
        <result property="moduleId" column="module_id"/>
    </resultMap>
    <select id="selectPageFieldList" resultMap="ProjectPageFieldResult">
        select f.page_id,f.field_id,f.page_row,f.page_column,p.page_name,p.module_id
        from project_page_field f
        left join project_page p ON (p.project_id = f.project_id AND p.page_id = f.page_id)
        ${ew.getCustomSqlSegment}
        order by f.page_row
    </select>
</mapper>
