<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.WorkflowTransitionMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.WorkflowTransitionVo" id="WorkflowTransitionInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="transitionId" column="transition_id"/>
        <result property="transitionName" column="transition_name"/>
        <result property="fromStateId" column="from_state_id"/>
        <result property="toStateId" column="to_state_id"/>
        <result property="displayOrder" column="display_order"/>
    </resultMap>
</mapper>
