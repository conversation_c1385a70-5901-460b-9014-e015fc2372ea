<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectAccountPermissionMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectAccountPermissionVo" id="ProjectAccountPermissionResult">
        <id column="key_id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="accounttype_id" property="accountTypeId" />
        <result column="type_id" property="typeId" />
        <result column="permission_id" property="permissionId" />
        <result column="option_id" property="optionId" />
        <result column="accounttype_name" property="accountTypeName" />
    </resultMap>

    <select id="selectPermissionList" parameterType="Integer" resultMap="ProjectAccountPermissionResult">
        select ap.key_id, ap.project_id, ap.accounttype_id, ap.type_id, ap.permission_id, ap.option_id, at.accounttype_name
        from project_account_permission ap
        left join project_accounttype at on at.project_id = ap.project_id and at.accounttype_id = ap.accounttype_id
        where ap.project_id = #{projectId}
        <if test="accounttypeId != null">
            and ap.accounttype_id = #{accounttypeId}
        </if>
        <if test="typeId != null">
            and ap.type_id = #{typeId}
        </if>
        order by ap.permission_id, ap.option_id
    </select>

    <delete id="deletePermissionByAccounttypeIds">
        DELETE FROM project_account_permission
        WHERE project_id = #{projectId}
        AND accounttype_id IN
        <foreach collection="accounttypeIds" item="accounttypeId" open="(" separator="," close=")">
            #{accounttypeId}
        </foreach>
    </delete>
</mapper>
