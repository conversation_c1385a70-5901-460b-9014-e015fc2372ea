<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.sequence.mapper.TableSequenceMapper">

    <select id="getMaxSequences" resultType="map">
        <foreach item="tableInfo" collection="tableInfos" separator="UNION ALL">
            SELECT
                '${tableInfo.tableName}' as tableName,
                COALESCE(MAX(${tableInfo.sequenceColumn}), 0) as maxSequence
            FROM ${tableInfo.tableName}
        </foreach>
    </select>

    <select id="getMaxSequenceByParent" resultType="int">
        SELECT COALESCE(MAX(${sequenceColumn}), 0)
        FROM ${tableName}
        WHERE ${parentIdColumn} = #{parentId}
    </select>

    <select id="getAllParentMaxSequences" resultType="map">
        <foreach item="tableInfo" collection="tableInfos" separator="UNION ALL">
            SELECT
                '${tableInfo.tableName}' as tableName,
                ${tableInfo.parentIdColumn} as parentId,
                COALESCE(MAX(${tableInfo.sequenceColumn}), 0) as maxSequence
            FROM ${tableInfo.tableName}
            WHERE ${tableInfo.parentIdColumn} IS NOT NULL
            GROUP BY ${tableInfo.parentIdColumn}
        </foreach>
    </select>

</mapper>
