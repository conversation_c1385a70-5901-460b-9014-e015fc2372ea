<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectGroupMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="ProjectGroupResultMap" type="org.dromara.common.servicetrack.domain.vo.ProjectGroupVo">
        <id column="key_id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="group_id" property="groupId" />
        <result column="group_name" property="groupName" />
        <result column="group_owner" property="groupOwner" />
        <result column="nick_name" property="groupOwnerName" />
    </resultMap>

    <select id="selectGroupList" parameterType="Integer" resultMap="ProjectGroupResultMap">
        select u.nick_name, u.user_name,g.key_id,g.project_id, g.group_id, g.group_name, g.group_owner
        from project_group g
        left join sys_user u on u.external_user_id = g.group_owner
        where g.project_id = #{projectId}
    </select>
</mapper>
