<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.UserInfoMapper">
    <resultMap type="org.dromara.servicetrack.domain.vo.UserInfoListVo" id="UserInfoListResult">
        <result column="nick_name" property="nickName"/>
        <result property="textFieldsJson" column="text_fields"/>
        <result property="datetimeFieldsJson" column="datetime_fields"/>
        <result property="selectionFieldsJson" column="selection_fields"/>
    </resultMap>
    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoListVo" id="ContactInfoListResult">
        <result column="nick_name" property="nickName"/>
        <result property="textFieldsJson" column="text_fields"/>
        <result property="datetimeFieldsJson" column="datetime_fields"/>
        <result property="selectionFieldsJson" column="selection_fields"/>
    </resultMap>
    <resultMap id="UserInfoDetailMap" type="org.dromara.servicetrack.domain.vo.STUserInfoVo">
        <!-- 基础字段映射 -->
        <id property="userId" column="user_id"/>
        <result column="user_name" property="userName"/>
        <result column="nick_name" property="nickName"/>
        <result column="dept_name" property="deptName"/>
        <result column="job_title" property="jobTitle"/>
        <result column="job_duty" property="jobDuty"/>
        <result column="support_team" property="supportTeam"/>
        <result column="primary_support" property="primarySupport"/>
        <result column="secondary_support" property="secondarySupport"/>
        <result column="is_vip" property="isVip"/>
        <!-- 自定义字段直接映射为字符串 -->
        <result column="datetime_fields" property="datetimeFieldsJson"/>
        <result column="text_fields" property="textFieldsJson"/>
        <result column="selection_fields" property="selectionFieldsJson"/>
    </resultMap>
    <resultMap id="ContactInfoDetailMap" type="org.dromara.servicetrack.domain.vo.ContactInfoVo">
        <!-- 基础字段映射 -->
        <id property="userId" column="user_id"/>
        <result column="user_name" property="userName"/>
        <result column="nick_name" property="nickName"/>
        <result column="dept_name" property="deptName"/>
        <!-- 自定义字段直接映射为字符串 -->
        <result column="datetime_fields" property="datetimeFieldsJson"/>
        <result column="text_fields" property="textFieldsJson"/>
        <result column="selection_fields" property="selectionFieldsJson"/>
    </resultMap>
    <resultMap type="org.dromara.servicetrack.domain.vo.STUserInfoVo" id="UserInfoAvatarMap">
        <result column="file_name" property="avatarUrl"/>
    </resultMap>
    <select id="selectPageUserList" resultMap="UserInfoListResult">
        <if test="(textFieldIds != null and textFieldIds.size() > 0) || (dateTimeFieldIds != null and dateTimeFieldIds.size() > 0) || (selectionFieldIds != null and selectionFieldIds.size() > 0)">
            WITH
            <trim suffixOverrides=",">
                <if test="textFieldIds != null and textFieldIds.size() > 0">
                    text_fields AS (
                    SELECT project_id, user_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
                    SEPARATOR ','
                    ) AS text_values,
                    MIN(text) AS min_text -- 添加一列用于排序
                    FROM user_info_text
                    WHERE project_id = #{projectId}
                    AND
                    user_id IN (SELECT u.external_user_id FROM sys_user u
                    <if test="userSqlSegment != null and userSqlSegment != ''">
                        WHERE  ${userSqlSegment}
                    </if> )
                    AND
                    field_id IN
                    <foreach item="fieldId" collection="textFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, user_id
                    ),
                </if>
                <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">

                    datetime_fields AS (
                    SELECT project_id, user_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', datetime, '"')
                    SEPARATOR ','
                    ) AS datetime_values,
                    MIN(datetime) AS min_datetime -- 添加一列用于排序
                    FROM user_info_datetime
                    WHERE  project_id = #{projectId}
                    AND
                    user_id IN (SELECT u.external_user_id FROM sys_user u
                    <if test="userSqlSegment != null and userSqlSegment != ''">
                        WHERE  ${userSqlSegment}
                    </if> )
                    AND  field_id IN
                    <foreach item="fieldId" collection="dateTimeFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, user_id
                    ),
                </if>
                <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
                    selection_fields AS (
                    SELECT project_id, user_id, field_id,
                    CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') AS choices,
                    MIN(choice_id) as min_choice_id
                    FROM user_info_selection
                    WHERE  project_id = #{projectId}
                    AND  field_id IN
                    <foreach item="fieldId" collection="selectionFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, user_id, field_id
                    ),
                    aggregated_selection AS (
                    SELECT
                    project_id,
                    user_id,
                    CONCAT('{', GROUP_CONCAT(CONCAT('"', field_id, '":', choices) SEPARATOR ','), '}') AS selection_data
                    FROM
                    selection_fields
                    GROUP BY
                    project_id, user_id
                    ),
                    filtered_fs AS (
                    SELECT
                    u.user_id,
                    s.project_id,
                    u.external_user_id,
                    MIN(fs.choice_name) AS choice_name -- 确保每个 user 唯一
                    FROM
                    sys_user u
                    LEFT JOIN
                    selection_fields s
                    ON
                    u.external_user_id = s.user_id
                    LEFT JOIN
                    project_field_selection fs
                    ON
                    s.project_id = fs.project_id AND s.field_id = fs.field_id AND s.min_choice_id = fs.choice_id
                    WHERE
                    s.project_id = #{projectId}
                    <if test="userSqlSegment != null and userSqlSegment != ''">
                        and ${userSqlSegment}
                    </if>
                    GROUP BY
                    u.external_user_id, s.project_id,  u.user_id
                    )
                </if>
            </trim>
        </if>
        select DISTINCT u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.phonenumber,
        u.status, u.del_flag, u.create_by, u.create_time,u.external_user_id,u.st_user_type,u.st_admin,
        d.dept_name,ui.job_duty,ui.job_title,ui.is_vip
        <if test="sortFieldId != null and sortFieldId == 9">
            ,submittedUser.nick_name
        </if>
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            , COALESCE(t.text_values, '{}') AS text_fields, min_text
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            ,COALESCE(d.datetime_values, '{}') AS datetime_fields, min_datetime
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            ,(
            SELECT CONCAT('{', GROUP_CONCAT(
            CONCAT('"', field_id, '":', choices)
            SEPARATOR ','
            ), '}')
            FROM selection_fields s
            WHERE s.project_id = #{projectId} AND s.user_id = u.external_user_id
            ) AS selection_fields,
            fs.choice_name -- 添加到 SELECT 列表中
        </if>
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join user_info ui on u.external_user_id = ui.user_id
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            LEFT JOIN text_fields t ON t.project_id = #{projectId} AND u.external_user_id = t.user_id
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            LEFT JOIN datetime_fields d ON d.project_id = #{projectId} AND u.external_user_id = d.user_id
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            LEFT JOIN aggregated_selection s  ON  s.project_id = #{projectId} AND u.external_user_id = s.user_id
            LEFT JOIN filtered_fs fs ON fs.project_id = #{projectId} AND u.external_user_id = fs.user_id
        </if>
        <choose>
            <when test="sortFieldId != null and sortFieldId == 9">
                LEFT JOIN sys_user submittedUser
                ON u.create_by = submittedUser.external_user_id
            </when>

        </choose>
        ${ew.getCustomSqlSegment}
    </select>
    <select id="selectPageUserList4Contact" resultMap="ContactInfoListResult">
        <if test="(textFieldIds != null and textFieldIds.size() > 0) || (dateTimeFieldIds != null and dateTimeFieldIds.size() > 0) || (selectionFieldIds != null and selectionFieldIds.size() > 0)">
            WITH
            <trim suffixOverrides=",">
                <if test="textFieldIds != null and textFieldIds.size() > 0">
                    text_fields AS (
                    SELECT project_id, contact_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
                    SEPARATOR ','
                    ) AS text_values,
                    MIN(text) AS min_text -- 添加一列用于排序
                    FROM contact_info_text
                    WHERE project_id = #{projectId}
                    AND
                    field_id IN
                    <foreach item="fieldId" collection="textFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, contact_id
                    ),
                </if>
                <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">

                    datetime_fields AS (
                    SELECT project_id, contact_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', datetime, '"')
                    SEPARATOR ','
                    ) AS datetime_values,
                    MIN(datetime) AS min_datetime -- 添加一列用于排序
                    FROM contact_info_datetime
                    WHERE  project_id = #{projectId}
                    AND  field_id IN
                    <foreach item="fieldId" collection="dateTimeFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, contact_id
                    ),
                </if>
                <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
                    selection_fields AS (
                    SELECT project_id, contact_id, field_id,
                    CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') AS choices,
                    MIN(choice_id) as min_choice_id
                    FROM contact_info_selection
                    WHERE  project_id = #{projectId}
                    AND  field_id IN
                    <foreach item="fieldId" collection="selectionFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, contact_id, field_id
                    ),
                    aggregated_selection AS (
                    SELECT
                    project_id,
                    contact_id,
                    CONCAT('{', GROUP_CONCAT(CONCAT('"', field_id, '":', choices) SEPARATOR ','), '}') AS selection_data
                    FROM
                    selection_fields
                    GROUP BY
                    project_id, contact_id
                    ),
                    filtered_fs AS (
                    SELECT
                    u.user_id,
                    s.project_id,
                    u.external_user_id,
                    MIN(fs.choice_name) AS choice_name -- 确保每个 user 唯一
                    FROM
                    sys_user u
                    LEFT JOIN
                    selection_fields s
                    ON
                    u.external_user_id = s.contact_id
                    LEFT JOIN
                    project_field_selection fs
                    ON
                    s.project_id = fs.project_id AND s.field_id = fs.field_id AND s.min_choice_id = fs.choice_id
                    WHERE
                    s.project_id = #{projectId}
                    <if test="userSqlSegment != null and userSqlSegment != ''">
                        and ${userSqlSegment}
                    </if>
                    GROUP BY
                    u.external_user_id, s.project_id,  u.user_id
                    )
                </if>
            </trim>
        </if>
        select DISTINCT u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.phonenumber,
        u.status, u.del_flag, u.create_by, u.create_time,u.external_user_id,u.st_user_type,u.st_admin,
        d.dept_name
        <if test="sortFieldId != null and sortFieldId == 9">
            ,submittedUser.nick_name
        </if>
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            , COALESCE(t.text_values, '{}') AS text_fields, min_text
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            ,COALESCE(d.datetime_values, '{}') AS datetime_fields, min_datetime
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            ,(
            SELECT CONCAT('{', GROUP_CONCAT(
            CONCAT('"', field_id, '":', choices)
            SEPARATOR ','
            ), '}')
            FROM selection_fields s
            WHERE s.project_id = #{projectId} AND s.contact_id = u.external_user_id
            ) AS selection_fields,
            fs.choice_name -- 添加到 SELECT 列表中
        </if>
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        <if test="customerId != null and customerId > 0 ">
            INNER JOIN customer_contact cc ON cc.project_id = #{projectId} AND cc.contact_id = u.external_user_id
            AND cc.customer_id = #{customerId}
        </if>
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            LEFT JOIN text_fields t ON t.project_id = #{projectId} AND u.external_user_id = t.contact_id
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            LEFT JOIN datetime_fields d ON d.project_id = #{projectId} AND u.external_user_id = d.contact_id
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            LEFT JOIN aggregated_selection s  ON  s.project_id = #{projectId} AND u.external_user_id = s.contact_id
            LEFT JOIN filtered_fs fs ON fs.project_id = #{projectId} AND u.external_user_id = fs.external_user_id
        </if>

        <choose>
            <when test="sortFieldId != null and sortFieldId == 9">
                LEFT JOIN sys_user submittedUser
                ON u.create_by = submittedUser.external_user_id
            </when>

        </choose>
        ${ew.getCustomSqlSegment}
    </select>
    <select id="getSTUserTypeById" resultType="Integer">
        select st_user_type from sys_user where user_id = #{userId}
    </select>
    <select id="getUserInfoAvatar" resultMap="UserInfoAvatarMap">
        select u.external_user_id,u.avatar, o.file_name from sys_user u
        left  join sys_oss o on u.avatar = o.oss_id
        where u.avatar is not null
        AND  u.external_user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="getUserInfoDetail" resultMap="UserInfoDetailMap">
        WITH
        base_user_info AS (
        select u.user_id, u.nick_name, u.user_name, u.email, u.phonenumber,u.dept_id, d.dept_name,
        u.status, u.del_flag, u.create_by, u.create_time,u.external_user_id,u.st_user_type,u.st_admin,
        ui.job_title,ui.job_duty,ui.support_team, ui.primary_support ,ui.secondary_support,ui.is_vip
        FROM sys_user u
        LEFT JOIN  user_info ui on u.external_user_id = ui.user_id
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.external_user_id = #{userId}
        ),
        datetime_fields AS (
        SELECT
        project_id,
        user_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', datetime, '"')
        SEPARATOR ','
        ) as datetime_values
        FROM user_info_datetime
        WHERE project_id = #{projectId} AND user_id = #{userId}
        GROUP BY project_id, user_id
        ),
        text_fields AS (
        SELECT
        project_id,
        user_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
        SEPARATOR ','
        ) as text_values
        FROM user_info_text
        WHERE project_id = #{projectId} AND user_id = #{userId}
        GROUP BY project_id, user_id
        ),
        selection_fields AS (
        SELECT
        project_id,
        user_id,
        field_id,
        CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') as choices
        FROM user_info_selection
        WHERE project_id = #{projectId} AND user_id = #{userId}
        GROUP BY project_id, user_id,field_id
        )
        SELECT
        u.*,
        CONCAT('{', COALESCE(d.datetime_values, ''), '}') as datetime_fields,
        CONCAT('{', COALESCE(t.text_values, ''), '}') as text_fields,
        (
        SELECT CONCAT('{',
        GROUP_CONCAT(
        CONCAT('"', field_id, '":', choices)
        SEPARATOR ','
        ),
        '}')
        FROM selection_fields s2
        WHERE s2.user_id = u.external_user_id
        ) as selection_fields
        FROM base_user_info u
        LEFT JOIN datetime_fields d ON  u.external_user_id = d.user_id
        LEFT JOIN text_fields t ON u.external_user_id = t.user_id
    </select>
    <select id="getUserInfoDetail4Contact" resultMap="ContactInfoDetailMap">
        WITH
        base_user_info AS (
        select u.user_id, u.nick_name, u.user_name, u.email, u.phonenumber,u.dept_id, d.dept_name,
        u.status, u.del_flag, u.create_by, u.create_time,u.external_user_id,u.st_user_type,u.st_admin
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.external_user_id = #{contactId}
        ),
        datetime_fields AS (
        SELECT
        project_id,
        contact_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', datetime, '"')
        SEPARATOR ','
        ) as datetime_values
        FROM contact_info_datetime
        WHERE project_id = #{projectId} AND contact_id = #{contactId}
        GROUP BY project_id, contact_id
        ),
        text_fields AS (
        SELECT
        project_id,
        contact_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
        SEPARATOR ','
        ) as text_values
        FROM contact_info_text
        WHERE project_id = #{projectId} AND contact_id = #{contactId}
        GROUP BY project_id, contact_id
        ),
        selection_fields AS (
        SELECT
        project_id,
        contact_id,
        field_id,
        CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') as choices
        FROM contact_info_selection
        WHERE project_id = #{projectId} AND contact_id = #{contactId}
        GROUP BY project_id, contact_id,field_id
        )
        SELECT
        u.*,
        CONCAT('{', COALESCE(d.datetime_values, ''), '}') as datetime_fields,
        CONCAT('{', COALESCE(t.text_values, ''), '}') as text_fields,
        (
        SELECT CONCAT('{',
        GROUP_CONCAT(
        CONCAT('"', field_id, '":', choices)
        SEPARATOR ','
        ),
        '}')
        FROM selection_fields s2
        WHERE s2.contact_id = u.external_user_id
        ) as selection_fields
        FROM base_user_info u
        LEFT JOIN datetime_fields d ON  u.external_user_id = d.contact_id
        LEFT JOIN text_fields t ON u.external_user_id = t.contact_id
    </select>
</mapper>
