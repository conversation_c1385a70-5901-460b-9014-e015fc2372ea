package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;


import org.dromara.servicetrack.domain.ItemTempGroupSetting;

/**
 * 业务对象 item_temp_groupsetting
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemTempGroupSetting.class)
public class ItemTempGroupSettingBo extends STBaseEntity {
    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * group id
     */
    @NotNull(message = "group id不能为空")
    private Integer groupId;

    /**
     * template id
     */
    private Integer templateId;

    /**
     * display order
     */
    private Integer displayOrder;
}
