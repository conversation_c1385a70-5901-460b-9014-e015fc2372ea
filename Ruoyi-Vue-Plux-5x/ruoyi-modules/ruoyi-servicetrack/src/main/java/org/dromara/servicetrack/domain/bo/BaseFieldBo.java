package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 字段基础对象 field base class
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseFieldBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;
}
