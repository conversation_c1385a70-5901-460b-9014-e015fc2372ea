package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoChangelogText;

/**
 * 联系人信息变更日志长文本业务对象 contact_info_changelog_text
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoChangelogText.class)
public class ContactInfoChangelogTextBo extends BaseChangeTextBo {
    /**
     * 联系人ID
     */
    @NotNull(message = "联系人ID不能为空")
    private Integer contactId;
}
