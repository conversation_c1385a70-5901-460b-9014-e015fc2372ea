package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import org.dromara.servicetrack.domain.ItemTempInfo;

/**
 * 业务对象 item_temp_info
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemTempInfo.class)
public class ItemTempInfoBo extends STBaseEntity {
    /**
     * ID
     */

    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 工作项目ID
     */
    @NotNull(message = "工作项目ID不能为空")
    private Integer workProjectId;
    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Integer templateId;

    /**
     * 模板名称
     */
    @Xss(message = "模板标题不能包含脚本字符")
    @NotBlank(message = "模板标题不能为空")
    @Size(min = 0, max = 200, message = "条目标题长度不能超过{max}个字符")
    private String templateName;


    /**
     * 模板 desc
     */
    @Xss(message = "模板描述内容不能包含脚本字符")
    @Size(min = 0, max = 2000, message = "模板描述内容的长度不能超过{max}")
    private String templateDesc;
    /**
     * 模板类型
     */
    private Integer templateType;

    /*
    * 条目信息
     */
    private ItemInfoBo itemInfoBo;
}
