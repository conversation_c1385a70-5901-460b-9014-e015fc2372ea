package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoSelection;

import java.io.Serializable;

/**
 * User Info selection 业务对象 user_info_selection
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoSelection.class)
public class UserInfoSelectionBo extends UserInfoFieldBo{
    /**
     * 选择ID
     */
    private Integer choiceId;
}
