package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemInfo.class)
public class ItemListVo extends CustomFieldsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "ID")
    private Long id;

    @ExcelProperty(value = "条目ID")
    private Integer itemId;

    /**
     * display id
     */
    @ExcelProperty(value = "显示ID")
    private String displayId;

    /**
     * item title
     */
    @JsonIgnore
    private String itemTitle;

    /**
     * module id
     */
    @ExcelProperty(value = "模块ID")
    private Integer moduleId;

    /**
     * type id
     */
    @ExcelProperty(value = "类型ID")
    private Integer typeId;

    /**
     * owner id
     */
    @JsonIgnore
    private Integer ownerId;

    /**
     * state id
     */
    @JsonIgnore
    private Integer stateId;

    /**
     * created time
     */
    @JsonIgnore
    private Date createdTime;

    /**
     * created by
     */
    @JsonIgnore
    private int createdBy;

    /**
     * modified by
     */
    @JsonIgnore
    private int modifiedBy;

    /**
     * modified time
     */
    @JsonIgnore
    private Date modifiedTime;

    @JsonIgnore
    private Integer closedBy;

    @JsonIgnore
    private Date closedTime;
    /**
     * customer id
     */
    @JsonIgnore
    private Integer customerId;

    /**
     * employee id
     */
    @JsonIgnore
    private Integer employeeId;
    /**
     * 条目字段数据
     */
    @ExcelProperty(value = "条目字段数据")
    private List<ListFieldVo> values;

    /*
    * 条目字段颜色
     */
    private String textColor;
    /*
     * 条目字段是否加粗
     */
    private Integer textBold;

}
