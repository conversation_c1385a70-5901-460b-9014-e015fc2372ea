package org.dromara.servicetrack.domain.vo;

import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemAmount;
import org.dromara.servicetrack.domain.ItemText;

/**
 * item_amount表的vo类
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemAmount.class)
public class ItemAmountVo extends ItemFieldVo {

    /**
     * field_value
     */
    private Double fieldValue;
}
