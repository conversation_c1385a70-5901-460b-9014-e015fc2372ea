package org.dromara.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.UserInfo;

import java.util.Date;


/**
 * sys_user simple info 对象
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfo.class)
public class UserInfoBo extends BaseInfoBo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 基础项目ID
     */
    private Integer projectId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空",groups = { AddGroup.class })
    @Size(min = 0, max = 30, message = "用户账号长度不能超过{max}个字符")
    private String userName;

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @NotBlank(message = "用户昵称不能为空",groups = { AddGroup.class })
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过{max}个字符")
    private String nickName;
    /**
     * 用户邮箱
     */
    @Email(message = "邮箱格式不正确",groups = { AddGroup.class})
    @Size(min = 0, max = 50, message = "邮箱长度不能超过{max}个字符")
    private String email;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * external user id
     */
    private Integer externalUserId;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    @NotNull(message = "用户类型不能为空", groups = { AddGroup.class})
    @Size(min = 1, max = 1, message = "用户类型长度不能超过{max}个字符",groups = { AddGroup.class  })
    private Integer[] stUserTypes;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    private Integer stUserType;

    public void SetSTUserType(){
        if( this.stUserTypes != null && this.stUserTypes.length > 0){
            if( this.stUserTypes.length == 1){
                var stUserType =this.stUserTypes[0];
                if( stUserType == 1 || stUserType == 2){
                    this.stUserType = stUserType;
                }
            }
            else if( this.stUserTypes.length == 2){
                var curstUserType = 0;
                for( var stUserType : this.stUserTypes){
                    curstUserType += stUserType;
                }
                if( curstUserType == 3){
                    this.stUserType = curstUserType;
                }
            }
        }
    }

    /**
     * service track 用户系统信息
     */
    private  STUserInfoBo stUserInfo;
}
