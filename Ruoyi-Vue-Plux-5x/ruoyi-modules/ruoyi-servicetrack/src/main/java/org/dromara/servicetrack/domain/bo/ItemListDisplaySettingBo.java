package org.dromara.servicetrack.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;
import java.util.stream.Stream;

/**
 * 条目列表显示设置业务对象
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemListDisplaySettingBo extends STBaseEntity {
    private String selectedFields;
    private List<Integer> selectedFieldIds;
    private List<ChoiceCombination> choiceCombinations;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ChoiceCombination extends STBaseEntity {
        private List<Choice> choices;
        private String color;
        private int textBold;
    }
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Choice extends STBaseEntity {
        private Integer fieldId;
        private Integer choiceId;
    }
    public void setSelectedListFields() {
        if( selectedFields != null && !selectedFields.isEmpty()){
            selectedFieldIds = Stream.of(selectedFields.split(",")).map(Integer::parseInt).toList();
        }
    }
}
