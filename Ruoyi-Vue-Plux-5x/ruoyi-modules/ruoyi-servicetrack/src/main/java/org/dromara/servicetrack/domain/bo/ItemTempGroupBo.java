package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import org.dromara.servicetrack.domain.ItemTempGroup;

/**
 * 业务对象 item_temp_group
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemTempGroup.class)
public class ItemTempGroupBo extends STBaseEntity{

    /**
     * ID
     */
    private Long id;

     /**
      * project id
      */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * group id
     */
    @NotNull(message = "group id不能为空")
    private Integer groupId;

    /**
     * group name
     */
    @Xss(message = "group name不能包含脚本字符")
    @NotBlank(message = "group name不能为空")
    @Size(min = 0, max = 100, message = "group name的长度不能超过{max}")
    private String groupName;

    /**
     * group desc
     */
    @Xss(message = "group desc不能包含脚本字符")
    @Size(min = 0, max = 2000, message = "group desc的长度不能超过{max}")
    private String groupDesc;

    /**
     * parent id
     */
    private Integer parentId;

    /**
     * display order
     */
    private Integer displayOrder;
}
