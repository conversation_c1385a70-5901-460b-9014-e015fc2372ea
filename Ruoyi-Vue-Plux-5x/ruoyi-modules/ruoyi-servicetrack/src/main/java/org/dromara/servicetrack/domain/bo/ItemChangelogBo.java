package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangelog;

import java.util.Date;
import java.util.List;

/**
 * 业务对象 item_changelog
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemChangelog.class)
public class ItemChangelogBo extends BaseChangelogBo{
    /**
     * item id
     */
    private  Integer itemId;
}
