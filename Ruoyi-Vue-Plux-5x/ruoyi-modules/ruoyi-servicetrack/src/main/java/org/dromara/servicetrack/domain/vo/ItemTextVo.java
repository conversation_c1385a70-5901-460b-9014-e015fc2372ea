package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemText;

import java.util.Date;

/**
 *视图对象 item_text
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemText.class)
public class ItemTextVo extends ItemFieldVo{
    /**
     * 文本内容
     */
    private String text;
}
