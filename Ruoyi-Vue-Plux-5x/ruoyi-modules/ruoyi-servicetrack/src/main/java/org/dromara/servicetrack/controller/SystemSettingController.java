package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.bo.SystemSettingBinderBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;
import org.dromara.servicetrack.domain.vo.SystemSettingVo;
import org.dromara.servicetrack.service.impl.SystemSettingServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统设置
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/system")
public class SystemSettingController extends BaseController {
    private final SystemSettingServiceImpl systemSettingService;

    /**
     * 查询系统设置列表
     * @param settingIds 设置ID串
     */
    @GetMapping("/list")
    public R<List<SystemSettingVo>> getSettingList(@NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {

        return R.ok(systemSettingService.selectSystemSettingList( settingIds));
    }
    /**
     *  更新系统设置类型
     */
    @Log(title = "系统设置管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping()
    public R<Boolean> editSetting(@Validated @RequestBody SystemSettingBinderBo bo) {
        return R.ok(systemSettingService.updateByBo(bo));
    }

    /**
     * 删除系统设置
     *
     * @param settingIds 设置ID串
     */
    @Log(title = "系统设置管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R<Void> removeSetting(@NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {
        return toAjax(systemSettingService.deleteWithValidByIds(settingIds, true));
    }
}
