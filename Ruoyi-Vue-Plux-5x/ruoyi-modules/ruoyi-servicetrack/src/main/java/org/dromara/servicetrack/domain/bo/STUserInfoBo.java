package org.dromara.servicetrack.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.STUserInfo;
import org.dromara.servicetrack.domain.UserInfo;

/**
 * user_info 对象
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = STUserInfo.class)
public class STUserInfoBo extends STBaseEntity {

    private Long id;
    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 职位
     */
    private Integer jobTitle;

    /**
     * 职位职责
     */
    private Integer jobDuty;

    /**
     * 团队
     */

    private Integer supportTeam;

    /**
     * 主负责人
     */
    private Integer primarySupport;

    /**
     * 副负责人
     */

    private Integer secondarySupport;

    /**
     * 是否VIP
     */
    private Integer isVip;
}
