package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoSelection;

import java.io.Serializable;

/**
 * 联系人信息选择字段业务对象 contact_info_selection
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoSelection.class)
public class ContactInfoSelectionBo extends ContactInfoFieldBo{
    /**
     * 选择ID
     */
    @NotNull(message = "选择ID不能为空")
    private Integer choiceId;
}
