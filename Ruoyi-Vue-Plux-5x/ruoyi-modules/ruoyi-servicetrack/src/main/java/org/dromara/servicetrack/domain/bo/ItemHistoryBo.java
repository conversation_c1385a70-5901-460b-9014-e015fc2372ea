package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.ItemHistory;

import java.util.Date;
import java.util.List;

/**
 * 业务对象 item_history
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemHistory.class)
public class ItemHistoryBo extends STBaseEntity {
    /**
     * ID
     */
    //@NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 条目ID
     */
    @NotNull(message = "条目ID不能为空")
    private Integer itemId;

    /**
     * seq no
     */
    @NotNull(message = "条目ID不能为空")
    private Integer seqNo;

    /**
     * datetime
     */
    private Date dateTime;

    /**
     * user_id
     */
    private Integer userId;

    /**
     * state_from
     */
    private Integer stateFrom;

    /**
     * state_to
     */
    private Integer stateTo;

    /**
     * owner_from
     */
    private Integer ownerFrom;

    /**
     * owner_to
     */
    private Integer ownerTo;
    /*
     * transition
     */
    private Integer transition;
}
