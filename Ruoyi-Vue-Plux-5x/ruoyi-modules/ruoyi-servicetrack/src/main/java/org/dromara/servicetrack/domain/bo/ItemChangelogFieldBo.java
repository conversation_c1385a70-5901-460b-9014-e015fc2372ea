package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangelog;
import org.dromara.servicetrack.domain.ItemChangelogField;

/**
 * 业务对象 item_changelog_field
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemChangelogField.class)
public class ItemChangelogFieldBo extends BaseChangelogFieldBo{
    /**
     * item id
     */
    private  Integer itemId;
}
