package org.dromara.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.dromara.servicetrack.domain.ItemText;

/**
 * 业务对象 item_text
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemText.class)
public class ItemTextBo extends ItemFieldBo {
    /**
     * text
     */
    private String text;
}
