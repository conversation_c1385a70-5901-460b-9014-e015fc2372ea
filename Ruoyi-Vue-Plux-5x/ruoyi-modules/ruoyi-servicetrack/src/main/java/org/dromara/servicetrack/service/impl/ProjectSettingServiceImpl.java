package org.dromara.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.servicetrack.domain.ProjectSetting;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.bo.ProjectSettingBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;
import org.dromara.servicetrack.mapper.ProjectSettingMapper;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ProjectSettingServiceImpl implements IProjectSettingService {
    private final ProjectSettingMapper baseMapper;

    @Override
    public ProjectSettingVo queryById(Long keyId) {
        return baseMapper.selectVoById(keyId);
    }

    @Override
    public TableDataInfo<ProjectSettingVo> queryPageList(Integer projectId, PageQuery pageQuery) {
        ProjectSettingBo bo = new ProjectSettingBo();
        bo.setProjectId(projectId);
        return TableDataInfo.<ProjectSettingVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ProjectSettingVo>) baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo)));
    }

    @Override
    public List<ProjectSettingVo> selectSettingList(Integer projectId,List<Integer> settingIds) {
        if(settingIds == null || settingIds.isEmpty()){
            throw new ServiceException("settingIds is null or empty");
        }
        ProjectSettingBo bo = new ProjectSettingBo();
        bo.setProjectId(projectId);
        QueryWrapper<ProjectSetting> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.in(true, "setting_id",settingIds);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public List<ProjectSettingVo> selectSettingList(List<Integer> projectIds, List<Integer> settingIds) {
        if(projectIds == null || projectIds.isEmpty()){
            throw new ServiceException("projectIds is null or empty");
        }
        if(settingIds == null || settingIds.isEmpty()){
            throw new ServiceException("settingIds is null or empty");
        }
        QueryWrapper<ProjectSetting> wrapper = Wrappers.query();
        wrapper.in(true, "project_id",projectIds);
        wrapper.in(true, "setting_id",settingIds);
        return baseMapper.selectVoList(wrapper);
    }

    private Wrapper<ProjectSetting> buildQueryWrapper(ProjectSettingBo bo) {
        QueryWrapper<ProjectSetting> wrapper = Wrappers.query();
        wrapper.eq(bo.getProjectId() != null, "project_id", bo.getProjectId());
        wrapper.eq(bo.getSettingId() != null, "setting_id", bo.getSettingId());
        wrapper.like(StringUtils.isNotBlank(bo.getSettingName()), "setting_name", bo.getSettingName());
        wrapper.eq(bo.getSettingOption() != null, "setting_option", bo.getSettingOption());
        return wrapper;
    }


    @Override
    public Boolean updateByBo(ProjectSettingBinderBo bo) {
        if (bo.getSettingList() == null || bo.getSettingList().isEmpty()) {
            return false;
        }
        var settingIds = bo.getSettingList().stream().map(ProjectSettingBinderBo.SettingInfoBo::getSettingId).toList();
        var settingListVo = selectSettingList(bo.getProjectId(), settingIds);
        List<ProjectSetting> insertList = new ArrayList<>();
        List<ProjectSetting> updateList = new ArrayList<>();
        for (var setting : bo.getSettingList()) {
            var settingId = setting.getSettingId();
            if (settingListVo.stream().noneMatch(s -> s.getSettingId().equals(settingId))) {
                //need to insert
                var projectSetting = bo.convertToProjectSetting(setting, null);
                insertList.add(projectSetting);
            }
            else{
                //need to update
                var settingVo = settingListVo.stream().filter(s -> s.getSettingId().equals(settingId)).findFirst();
                if (settingVo.isPresent()) {
                    var projectSetting = bo.convertToProjectSetting(setting, settingVo.get().getId());
                    updateList.add(projectSetting);
                }
            }
        }
        if (!insertList.isEmpty()) {
            baseMapper.insertBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            baseMapper.updateBatchById(updateList);
        }
        return true;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteByIds(keyIds) > 0;
    }
}
