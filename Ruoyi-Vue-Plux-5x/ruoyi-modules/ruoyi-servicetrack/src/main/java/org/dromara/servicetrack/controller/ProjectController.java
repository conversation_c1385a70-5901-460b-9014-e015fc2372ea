package org.dromara.servicetrack.controller;

import io.swagger.v3.oas.models.security.SecurityScheme;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.eProjectSetting;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.service.*;
import org.dromara.common.servicetrack.service.impl.MailAccountCache;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.bo.UserSettingBinderBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;
import org.dromara.servicetrack.domain.vo.UserSettingVo;
import org.dromara.servicetrack.logic.ProjectLogic;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 项目管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/project")
public class ProjectController extends BaseController {
    private final IProjectInfoService projectInfoService;
    private final IProjectItemTypeService projectItemTypeService;
    private final IProjectSettingService projectSettingService;
    private final IUserSettingService userSettingService;
    private final INotificationService notificationService;
    private final IWorkflowInfoService workflowInfoService;
    private final MailAccountCache mailAccountCache;
    /**
     * 获取ID项目详情
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ProjectInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(projectInfoService.queryById(id));
    }
    /**
     * 根据Project Id获得项目详情
     */
    @GetMapping("/getInfo")
    public R<ProjectInfoVo> getProjectInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return R.ok(projectInfoService.selectProjectInfoById(projectId));
    }
    /**
     * 查询项目列表
     */
    @GetMapping("/list")
    public List<ProjectInfoVo> list() {
        ProjectInfoBo bo = new ProjectInfoBo();
        return projectInfoService.queryList(bo);
    }
    @GetMapping("/listByUser")
    public R<List<ProjectInfoVo>> listByUser(@RequestParam(required = false) Integer getOpenIncidentCount) {
        var projectInfos = projectInfoService.queryListByUser(getOpenIncidentCount);
        if( projectInfos == null || projectInfos.isEmpty())
            return R.ok();
        List<Integer> workProjectIds = new ArrayList<>();
        for (ProjectInfoVo projectInfo : projectInfos) {
            if( projectInfo.getChildren() == null || projectInfo.getChildren().isEmpty())
                continue;;
            workProjectIds.addAll(projectInfo.getChildren().stream().map(ProjectInfoVo::getProjectId).toList());
        }
        if(!workProjectIds.isEmpty()){
            Integer settingId = eProjectSetting.Project_Alias.getValue();
            List<Integer> listSetting = new ArrayList<>();
            listSetting.add(settingId);
           var projectAlias = projectSettingService.selectSettingList(workProjectIds, listSetting);
           if(projectAlias != null && !projectAlias.isEmpty()){
               for (ProjectInfoVo projectInfo : projectInfos) {
                   if( projectInfo.getChildren() == null || projectInfo.getChildren().isEmpty())
                       continue;;
                   for (ProjectInfoVo projectInfoVo : projectInfo.getChildren()){
                       var aliasSetting = projectAlias.stream().filter(x -> x.getProjectId().equals(projectInfoVo.getProjectId())).findFirst().orElse( null);
                       String alias = projectInfoVo.getProjectName();
                       if( aliasSetting != null){
                           alias = aliasSetting.getSettingContent();
                       }
                       //substring 4 string
                       if( alias.length() > 4)
                         alias = alias.substring(0,4);
                       projectInfoVo.setAlias(alias);
                   }
               }
           }
        }

        return R.ok(projectInfos);
    }

    /**
     * 新增项目
     */
    @Log(title = "项目管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectInfoBo bo) {
        return R.ok(projectInfoService.insertByBo(bo));
    }

    /**
     * 修改项目
     */
    @Log(title = "项目管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated @RequestBody ProjectInfoBo bo) {
        return R.ok(projectInfoService.updateByBo(bo));
    }

    /**
     * 删除项目
     *
     * @param ids 主键串
     */
    @Log(title = "项目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectInfoService.deleteWithValidByIds(List.of(ids), true));
    }
    /**
     * 获取项目用户列表
     *
     * @param projectId 项目ID
     * @return 项目用户列表
     */
    @GetMapping("/getMemberList")
    public R<List<ProjectMemberVo>> getOwnerList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        return R.ok(projectLogic.getMemberList());
    }

    /**
     * 获取项目状态列表
     *
     * @param projectId 项目ID
     * @return 项目状态列表
     */
    @GetMapping("/getStateList")
    public R<List<WorkflowStateBaseInfoVo>> getStateList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        return R.ok(workflowInfoService.getWorkflowStateList(projectId));
    }
    /**
     * 获取项目 transition列表
     *
     * @param projectId 项目ID
     * @return 项目 transition列表
     */
    @GetMapping("/getTransitionList")
    public R<List<WorkflowTransitionVo>> getTransitionList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        return R.ok(projectLogic.getTransitionList());
    }
    /**
     * 重置在缓存中的项目设置
     *
     * @param projectId 项目ID: 如果为空，则重置所有项目设置
     */
    @GetMapping("/ResetProjectSettings")
    public R<Void> ResetProjectSettings(
    @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
        ProjectLogic projectLogic = new ProjectLogic(projectId, Objects.requireNonNull(LoginHelper.getLoginUser()).getExternalUserId());
        projectLogic.resetProjectSetting();
        mailAccountCache.remove(projectId);
        return R.ok();
    }
    /**
     * 重置在缓存中的邮件账户设置
     *
     * @param projectId 项目ID
     */
    @GetMapping("/ResetMailAccountCache")
    public R<Void> ResetMailAccountCache(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
         mailAccountCache.remove(projectId);
        return R.ok();
    }

    //item type
    @GetMapping("/itemType/list")
    public R<List<ProjectItemTypeVo>> getItemTypeList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId){
         return R.ok(projectItemTypeService.selectItemTypeList(projectId));
    }
    /**
     * 新增项目条目类型
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/itemType")
    public R<Integer> addItemType(@Validated @RequestBody ProjectItemTypeBo bo) {
        return R.ok(projectItemTypeService.insertByBo(bo));
    }

    /**
     * 修改项目条目类型
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/itemType")
    public R<Integer> editItemType(@Validated @RequestBody ProjectItemTypeBo bo) {
        return R.ok(projectItemTypeService.updateByBo(bo));
    }

    /**
     * 删除项目条目类型
     *
     * @param ids 主键串
     */
    @Log(title = "项目条目类型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/itemType/{ids}")
    public R<Void> removeItemType(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectItemTypeService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of item type

    // project Setting
    /**
     * 查询项目设置列表
     */
    @GetMapping("/setting/list")
    public R<List<ProjectSettingInCacheVo>> getSettingList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                 @NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {

        return R.ok(projectSettingService.selectSettingList(projectId, settingIds));
    }
    /**
     *  更新项目设置类型
     */
    @Log(title = "项目设置管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/setting")
    public R<Void> editSetting(@Validated @RequestBody ProjectSettingBinderBo bo) {
        return toAjax(projectSettingService.updateByBo(bo));
    }

    /**
     * 删除项目设置
     *
     * @param ids 主键串
     */
    @Log(title = "项目设置管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/setting/{ids}")
    public R<Void> removeSetting(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectSettingService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of project Setting

    //user setting
    /**
     * 查询项目用户设置列表
     */
    @GetMapping("/userSetting/list")
    public R<List<UserSettingVo>> getUserSettinglist(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                  @NotNull(message = "userId不能为空") @RequestParam Integer userId,
                                                  @NotNull(message = "SettingIds不能为空") @RequestParam List<Integer> settingIds) {

        return R.ok(userSettingService.selectUserSettingList(projectId,userId, settingIds));
    }
    /**
     *  更新项目用户设置类型
     */
    @Log(title = "项目用户设置管理", businessType = BusinessType.UPDATE)
    @PostMapping("/userSetting")
    public R<Void> editUserSetting(@Validated @RequestBody UserSettingBinderBo bo) {
        return toAjax(userSettingService.updateUserSetting(bo));
    }

    /**
     * 删除项目用户设置
     *
     * @param ids 主键串
     */
    @Log(title = "项目用户设置管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/userSetting/{ids}")
    public R<Void> removeUserSetting(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(userSettingService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of user setting

    //start of Notification setting
    /**
     * 查询项目通知规则列表
     */
    @GetMapping("/Notification/list")
    public R<List<NotificationRuleListItemVo>> getNotificationRuleList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                    @NotNull(message = "ObjectId不能为空") @RequestParam Integer objectId) {
        return R.ok(notificationService.selectRuleList(projectId, objectId));
    }
    /**
     * 查询项目通知规则
     */
    @GetMapping("/Notification/getOneRule")
    public R<NotificationRuleListItemVo> getNotificationOneRule(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                                       @NotNull(message = "RuleId不能为空") @RequestParam Integer ruleId) {
        return R.ok(notificationService.selectOneRule(projectId, ruleId));
    }
    /**
     * 新增项目通知规则
     */
    @Log(title = "项目通知规则管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/Notification")
    public R<Integer> addNotificationRule(@Validated @RequestBody NotificationRuleBo bo) {
        return R.ok(notificationService.insertRuleByBo(bo));
    }

    /**
     * 修改项目通知规则
     */
    @Log(title = "项目通知规则管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/Notification")
    public R<Integer> editNotificationRule(@Validated @RequestBody NotificationRuleBo bo) {
        return R.ok(notificationService.updateRuleByBo(bo));
    }

    /**
     * 删除项目通知规则
     *
     * @param ids 主键串
     */
    @Log(title = "项目通知规则管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/Notification/{ids}")
    public R<Void> removeNotificationRule(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(notificationService.deleteRules(List.of(ids), true));
    }
    //end of Notification setting

    //start of Email Template setting
    /**
     * 查询项目邮件模板列表
     */
    @GetMapping("/EmailTemplate/list")
    public R<List<NotificationEmailTemplateListItemVo>> getEmailTemplateList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return R.ok(notificationService.selectEmailTemplateList(projectId));
    }
    /**
     * 查询项目邮件模板
     */
    @GetMapping("/EmailTemplate/getOneTemplate")
    public R<NotificationEmailTemplateVo> getOneEmailTemplate(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                                @NotNull(message = "TemplateId不能为空") @RequestParam Integer templateId) {
        return R.ok(notificationService.selectEmailTemplate(projectId, templateId));
    }
    /**
     * 新增项目邮件模板
     */
    @Log(title = "项目邮件模板管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/EmailTemplate")
    public R<Integer> addEmailTemplate(@Validated @RequestBody NotificationEmailTemplateBo bo) {
        return R.ok(notificationService.insertEmailTemplateByBo(bo));
    }

    /**
     * 修改项目邮件模板
     */
    @Log(title = "项目邮件模板管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/EmailTemplate")
    public R<Integer> editEmailTemplate(@Validated @RequestBody NotificationEmailTemplateBo bo) {
        return R.ok(notificationService.updateEmailTemplateByBo(bo));
    }

    /**
     * 删除邮件模板规则
     *
     * @param ids 主键串
     */
    @Log(title = "项目邮件模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/EmailTemplate/{ids}")
    public R<Void> removeEmailTemplate(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(notificationService.deleteEmailTemplates(List.of(ids), true));
    }
    //end of Email Template setting

    //Start of Workflow setting
    /*
     * 查询项目工作流
     */
    @GetMapping("workflow/getOneWorkflow")
    public R<WorkflowInfoVo> getWorkflowInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "WorkflowId不能为空") @RequestParam Integer workflowId) {
        return R.ok(workflowInfoService.getWorkflowInfo(projectId, workflowId));
    }
    @GetMapping("workflow/getWorkflowStateProperty")
    public R<WorkflowStatePropertyVo> getWorkflowStateProperty(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "StateId不能为空") @RequestParam Integer stateId) {
        return R.ok(workflowInfoService.getWorkflowStateProperty(projectId, stateId));
    }
    @GetMapping("workflow/getWorkflowTransitionProperty")
    public R<WorkflowTransitionPropertyVo> getWorkflowTransitionProperty(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "TransitionId不能为空") @RequestParam Integer transitionId) {
        return R.ok(workflowInfoService.getWorkflowTransitionProperty(projectId, transitionId));
    }
    /*
     * 修改项目工作流
     */
    @RepeatSubmit()
    @PostMapping("/workflow")
    public R<Integer> editWorkflow(@Validated @RequestBody WorkflowInfoBo bo) {
        return R.ok(workflowInfoService.updateWorkflowInfo(bo));
    }
    /*
     * 修改项目工作流状态字段
    */
    @RepeatSubmit()
    @PostMapping("/workflow/updateStateField")
    public R<Integer> editWorkflowStateField(@Validated @RequestBody WorkflowStateFieldBinderBo bo) {
        return R.ok(workflowInfoService.updateWorkflowStateField(bo));
    }
    /*
     * 修改项目工作流状态负责人
    */
    @RepeatSubmit()
    @PostMapping("/workflow/updateStateOwners")
    public R<Integer> editWorkflowStateOwner(@Validated @RequestBody WorkflowStateOwnersBinderBo bo) {
        return R.ok(workflowInfoService.updateWorkflowStateOwners(bo));
    }
    /*
     * 修改项目工作流流转字段
    */
    @RepeatSubmit()
    @PostMapping("/workflow/updateTransitionField")
    public R<Integer> editWorkflowTransitionField(@Validated @RequestBody WorkflowTransitionFieldBinderBo bo) {
        return R.ok(workflowInfoService.updateWorkflowTransitionField(bo));
    }
    /*
     * 修改项目工作流流转权限
    */
    @RepeatSubmit()
    @PostMapping("/workflow/updateTransitionPermission")
    public R<Integer> editWorkflowTransitionPermission(@Validated @RequestBody WorkflowTransitionPermissionBinderBo bo) {
        return R.ok(workflowInfoService.updateWorkflowTransitionPermission(bo));
    }
    /*
     * 删除项目工作流状态字段
    */
    @DeleteMapping("/workflow/stateField/{ids}")
    public R<Integer> removeWorkflowStateField(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return R.ok(workflowInfoService.deleteWorkflowStateField(List.of(ids)));
    }
    /*
     * 删除项目工作流状态负责人
    */
    @DeleteMapping("/workflow/stateOwners/{ids}")
    public R<Integer> removeWorkflowStateOwners(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return R.ok(workflowInfoService.deleteWorkflowStateOwners(List.of(ids)));
    }
    /*
     * 删除项目工作流流转字段
    */
    @DeleteMapping("/workflow/transitionField/{ids}")
    public R<Integer> removeWorkflowTransitionField(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return R.ok(workflowInfoService.deleteWorkflowTransitionField(List.of(ids)));
    }
    /*
     * 删除项目工作流流转权限
    */
    @DeleteMapping("/workflow/transitionPermission/{ids}")
    public R<Integer> removeWorkflowTransitionPermission(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return R.ok(workflowInfoService.deleteWorkflowTransitionPermission(List.of(ids)));
    }
    //End of Workflow setting
}
