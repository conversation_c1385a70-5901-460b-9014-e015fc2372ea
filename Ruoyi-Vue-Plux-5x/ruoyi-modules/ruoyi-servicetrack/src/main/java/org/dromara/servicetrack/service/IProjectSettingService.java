package org.dromara.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;

import java.util.Collection;
import java.util.List;

public interface IProjectSettingService {
    /**
     * 查询项目设置列表
     *
     * @return 项目设置列表
     */
    ProjectSettingVo queryById(Long keyId);
    /**
     * 查询项目设置列表
     *
     * @return 项目设置列表
     */
    TableDataInfo<ProjectSettingVo> queryPageList(Integer projectId, PageQuery pageQuery);
    /**
     * 查询项目设置列表
     *
     * @return 项目设置列表
     */
    List<ProjectSettingVo> selectSettingList(Integer projectId,List<Integer> settingIds);

    /**
     * 查询多个项目设置列表
     *
     * @return 项目设置列表
     */
    List<ProjectSettingVo> selectSettingList(List<Integer> projectIds,List<Integer> settingIds);
    /**
     * 更新项目设置
     *
     */
    Boolean updateByBo(ProjectSettingBinderBo bo);
    /**
     * 删除项目设置
     *
     */
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);
}
