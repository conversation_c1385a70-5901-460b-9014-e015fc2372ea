package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.servicetrack.domain.FolderInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 文件夹信息视图对象 folder_info
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FolderInfo.class)
public class FolderInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 文件夹ID
     */
    private Integer folderId;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 文件夹类型
     */
    private Integer folderType;

    /**
     * 文件夹类型名称
     */
    private String folderTypeName;

    /**
     * 文件夹描述
     */
    private String folderDescription;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 创建者
     */
    private Integer createdBy;

    /**
     * 创建者名称
     */
    private String createdByName;
}
