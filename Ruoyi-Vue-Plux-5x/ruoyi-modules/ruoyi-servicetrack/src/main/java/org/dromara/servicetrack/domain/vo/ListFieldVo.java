package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListFieldVo implements Serializable {
    /*
      * field id
     */
    @ExcelProperty(value = "字段ID")
    private  Integer id;

    /*
      * choice id： Integer for dropdown, List<Integer> for multiselection, others is null
     */
    @ExcelProperty(value = "选项值")
    private Object choiceId;
    /*
      * field value
     */
    @ExcelProperty(value = "字段值")
    private  String value;
}
