package org.dromara.servicetrack.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.dromara.common.core.xss.Xss;

@Data
public class UserSettingBo {
    private Long id;
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;
    @NotNull(message = "用户ID不能为空")
    private Integer userId;
    @NotNull(message = "设置ID不能为空")
    private Integer settingId;

    @NotNull(message = "设置名称不能为空")
    @Xss(message = "设置名称不能包含脚本字符")
    @NotBlank(message = "设置名称不能为空")
    @Size(min = 1, max = 500, message = "设置名称的长度不能超过{max}")
    private String settingName;
    /*
     *设置选项
     */
    private Integer settingOption;

    /*
     *设置内容
     */
    private String settingContent;
}
